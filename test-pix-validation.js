/**
 * Teste de Validação PIX
 * Verifica se os BR Codes gerados seguem o padrão bancário brasileiro
 */

// Função para validar BR Code PIX
function validatePixBRCode(brCode) {
  const errors = [];
  const warnings = [];

  // 1. Verificar tamanho mínimo e máximo
  if (brCode.length < 50) {
    errors.push("BR Code muito curto (mínimo 50 caracteres)");
  }
  if (brCode.length > 512) {
    errors.push("BR Code muito longo (máximo 512 caracteres)");
  }

  // 2. Verificar se contém apenas caracteres válidos para a estrutura
  // BR Code pode conter letras, números e alguns símbolos específicos
  if (!/^[0-9A-Za-z\.\-\+\*\@\$\%\/:_ ]+$/.test(brCode)) {
    errors.push("BR Code contém caracteres inválidos");
  }

  // 3. Verificar estrutura básica
  const fields = parsePixFields(brCode);

  // Verificar campos obrigatórios
  const requiredFields = ["00", "01", "26", "52", "53", "58", "59", "60", "62", "63"];
  for (const field of requiredFields) {
    if (!fields[field]) {
      errors.push(`Campo obrigatório ${field} não encontrado`);
    }
  }

  // 4. Verificar valores específicos
  if (fields["00"] && fields["00"] !== "01") {
    errors.push('Payload Format Indicator deve ser "01"');
  }

  if (fields["01"] && !["11", "12"].includes(fields["01"])) {
    errors.push('Point of Initiation Method deve ser "11" (estático) ou "12" (dinâmico)');
  }

  if (fields["52"] && fields["52"] !== "0000") {
    warnings.push('Merchant Category Code não é "0000" (padrão PIX)');
  }

  if (fields["53"] && fields["53"] !== "986") {
    errors.push('Currency Code deve ser "986" (BRL)');
  }

  if (fields["58"] && fields["58"] !== "BR") {
    errors.push('Country Code deve ser "BR"');
  }

  // 5. Verificar CRC
  const brCodeWithoutCRC = brCode.substring(0, brCode.length - 4);
  const providedCRC = brCode.substring(brCode.length - 4);
  const calculatedCRC = calculateCRC16(brCodeWithoutCRC);

  if (providedCRC !== calculatedCRC) {
    errors.push(`CRC inválido. Esperado: ${calculatedCRC}, Encontrado: ${providedCRC}`);
  }

  // 6. Verificar campo PIX (26)
  if (fields["26"]) {
    const pixFields = parsePixSubFields(fields["26"]);
    if (!pixFields["00"] || pixFields["00"] !== "br.gov.bcb.pix") {
      errors.push('Campo PIX deve conter "br.gov.bcb.pix"');
    }
    if (!pixFields["01"]) {
      errors.push("Chave PIX não encontrada no campo 26");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    fields
  };
}

// Função para parsear campos PIX
function parsePixFields(brCode) {
  const fields = {};
  let position = 0;

  while (position < brCode.length - 4) {
    // -4 para excluir CRC
    if (position + 4 > brCode.length) break;

    const tag = brCode.substring(position, position + 2);
    const length = parseInt(brCode.substring(position + 2, position + 4));

    if (isNaN(length) || position + 4 + length > brCode.length) break;

    const value = brCode.substring(position + 4, position + 4 + length);
    fields[tag] = value;

    position += 4 + length;
  }

  return fields;
}

// Função para parsear subcampos PIX (campo 26)
function parsePixSubFields(pixData) {
  const fields = {};
  let position = 0;

  while (position < pixData.length) {
    if (position + 4 > pixData.length) break;

    const tag = pixData.substring(position, position + 2);
    const length = parseInt(pixData.substring(position + 2, position + 4));

    if (isNaN(length) || position + 4 + length > pixData.length) break;

    const value = pixData.substring(position + 4, position + 4 + length);
    fields[tag] = value;

    position += 4 + length;
  }

  return fields;
}

// Função para calcular CRC16
function calculateCRC16(data) {
  let crc = 0xffff;
  const dataBytes = new TextEncoder().encode(data);

  for (const byte of dataBytes) {
    crc ^= byte << 8;
    for (let i = 0; i < 8; i++) {
      if (crc & 0x8000) {
        crc = (crc << 1) ^ 0x1021;
      } else {
        crc <<= 1;
      }
    }
  }

  return (crc & 0xffff).toString(16).toUpperCase().padStart(4, "0");
}

// Função para gerar BR Code de teste (aplicação original)
function generateTestBRCode() {
  const getValue = (tag, value) => {
    return `${tag}${value.length.toString().padStart(2, "0")}${value}`;
  };

  const formattedText = (text) => {
    return text
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, "");
  };

  const crcCompute = (data) => {
    let crc = 0xffff;
    const dataBytes = new TextEncoder().encode(data);

    for (const byte of dataBytes) {
      crc ^= byte << 8;
      for (let i = 0; i < 8; i++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ 0x1021;
        } else {
          crc <<= 1;
        }
      }
    }

    return (crc & 0xffff).toString(16).toUpperCase().padStart(4, "0");
  };

  const data = {
    pixKey: "***********",
    receiverName: "João Silva",
    receiverCity: "São Paulo",
    amount: 10.5,
    reference: "TESTE123",
    description: "Pagamento teste"
  };

  // Build account information
  const basePix = getValue("00", "br.gov.bcb.pix");
  let infoString = getValue("01", data.pixKey);

  if (data.description) {
    infoString += getValue("02", formattedText(data.description));
  }

  const accountInfo = getValue("26", basePix + infoString);

  // Build additional data field
  const txid = data.reference || "***";
  const additionalData = getValue("62", getValue("05", formattedText(txid)));

  // Build complete BR Code
  let resultString = getValue("00", "01"); // Payload Format Indicator
  resultString += getValue("01", "11"); // Point of Initiation Method (static)
  resultString += accountInfo;
  resultString += getValue("52", "0000"); // Merchant Category Code
  resultString += getValue("53", "986"); // Transaction Currency (BRL)

  if (data.amount > 0) {
    resultString += getValue("54", data.amount.toFixed(2));
  }

  resultString += getValue("58", "BR"); // Country Code
  resultString += getValue("59", formattedText(data.receiverName)); // Merchant Name
  resultString += getValue("60", formattedText(data.receiverCity)); // Merchant City
  resultString += additionalData;
  resultString += "6304"; // CRC placeholder

  const finalBRCode = resultString + crcCompute(resultString);

  return finalBRCode;
}

// Executar teste
function runPixValidationTest() {
  console.log("🧪 Iniciando teste de validação PIX...\n");

  const testBRCode = generateTestBRCode();
  console.log("📋 BR Code de teste gerado:");
  console.log(testBRCode);
  console.log(`📏 Tamanho: ${testBRCode.length} caracteres\n`);

  const validation = validatePixBRCode(testBRCode);

  console.log("🔍 Resultado da validação:");
  console.log(`✅ Válido: ${validation.isValid ? "SIM" : "NÃO"}\n`);

  if (validation.errors.length > 0) {
    console.log("❌ Erros encontrados:");
    validation.errors.forEach((error) => console.log(`  - ${error}`));
    console.log("");
  }

  if (validation.warnings.length > 0) {
    console.log("⚠️ Avisos:");
    validation.warnings.forEach((warning) => console.log(`  - ${warning}`));
    console.log("");
  }

  console.log("📊 Campos encontrados:");
  Object.entries(validation.fields).forEach(([tag, value]) => {
    console.log(`  ${tag}: ${value}`);
  });

  return validation;
}

// Executar se estiver no Node.js
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    validatePixBRCode,
    generateTestBRCode,
    runPixValidationTest
  };

  // Executar teste se chamado diretamente
  if (require.main === module) {
    runPixValidationTest();
  }
}

// Executar se estiver no browser
if (typeof window !== "undefined") {
  window.PixValidator = {
    validatePixBRCode,
    generateTestBRCode,
    runPixValidationTest
  };
}
