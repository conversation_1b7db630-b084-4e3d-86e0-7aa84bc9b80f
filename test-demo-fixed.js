// Teste para verificar se a correção no demo.html funcionou

// Implementação corrigida (baseada no demo.html após a correção)
function generateBRCodeFixed(data) {
  const formatBRCodeField = (tag, value) => {
    const length = String(value.length).padStart(2, '0');
    return `${tag}${length}${value}`;
  };

  const formatText = (text) => {
    return text
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '') // Keep only allowed characters
      .trim();
  };

  const createAccountInformation = (data) => {
    const basePix = formatBRCodeField('00', 'br.gov.bcb.pix');
    let infoString = formatBRCodeField('01', data.pixKey);

    if (data.description) {
      infoString += formatBRCodeField('02', formatText(data.description));
    }

    return formatBRCodeField('26', basePix + infoString);
  };

  const createAdditionalDataField = (data) => {
    const txid = data.reference ? formatText(data.reference) : '***';
    return formatBRCodeField('62', formatBRCodeField('05', txid));
  };

  const calculateCRC16 = (data) => {
    let crc = 0xFFFF;
    for (let i = 0; i < data.length; i++) {
      crc ^= data.charCodeAt(i) << 8;
      for (let j = 0; j < 8; j++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ 0x1021;
        } else {
          crc <<= 1;
        }
      }
    }
    return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, '0');
  };

  const fields = [
    '000201', // Payload Format Indicator
    '010211', // Point of Initiation Method
    createAccountInformation(data),
    '********', // Merchant Category Code
    '5303986', // Currency Code (BRL)
    // CORRIGIDO: Usando formatBRCodeField
    data.amount > 0 ? formatBRCodeField('54', data.amount.toFixed(2)) : '',
    '5802BR', // Country Code
    // CORRIGIDO: Usando formatBRCodeField com formatText
    formatBRCodeField('59', formatText(data.receiverName)),
    formatBRCodeField('60', formatText(data.receiverCity)),
    createAdditionalDataField(data),
    '6304' // CRC placeholder
  ].filter(Boolean).join('');

  const crc = calculateCRC16(fields);
  return fields + crc;
}

// Implementação da versão original (funcional) para comparação
function generateBRCodeOriginal(data) {
  const getValue = (tag, value) => {
    return `${tag}${value.length.toString().padStart(2, "0")}${value}`;
  };

  const formattedText = (text) => {
    return text
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, "");
  };

  const crcCompute = (data) => {
    let crc = 0xffff;
    const dataBytes = new TextEncoder().encode(data);

    for (const byte of dataBytes) {
      crc ^= (byte << 8);
      for (let i = 0; i < 8; i++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ 0x1021;
        } else {
          crc <<= 1;
        }
      }
    }

    return (crc & 0xffff).toString(16).toUpperCase().padStart(4, "0");
  };

  // Build account information
  const basePix = getValue("00", "br.gov.bcb.pix");
  let infoString = getValue("01", data.pixKey);

  if (data.description) {
    infoString += getValue("02", formattedText(data.description));
  }

  const accountInfo = getValue("26", basePix + infoString);

  // Build additional data field
  const txid = data.reference || "***";
  const additionalData = getValue("62", getValue("05", formattedText(txid)));

  // Build complete BR Code
  let resultString = getValue("00", "01"); // Payload Format Indicator
  resultString += getValue("01", "11"); // Point of Initiation Method (static)
  resultString += accountInfo;
  resultString += getValue("52", "0000"); // Merchant Category Code
  resultString += getValue("53", "986"); // Transaction Currency (BRL)

  if (data.amount > 0) {
    resultString += getValue("54", data.amount.toFixed(2));
  }

  resultString += getValue("58", "BR"); // Country Code
  resultString += getValue("59", formattedText(data.receiverName)); // Merchant Name
  resultString += getValue("60", formattedText(data.receiverCity)); // Merchant City
  resultString += additionalData;
  resultString += "6304"; // CRC placeholder

  const finalBRCode = resultString + crcCompute(resultString);

  return finalBRCode;
}

console.log("=== TESTE DA CORREÇÃO DO DEMO.HTML ===\n");

const testCases = [
  {
    name: "CPF com valor",
    data: {
      pixKey: "***********",
      receiverName: "João Silva",
      receiverCity: "São Paulo",
      amount: 150.75,
      reference: "TEST001",
      description: "Pagamento teste"
    }
  },
  {
    name: "Email sem valor",
    data: {
      pixKey: "<EMAIL>",
      receiverName: "Maria Santos",
      receiverCity: "Rio de Janeiro",
      amount: 0,
      reference: "REF123",
      description: ""
    }
  },
  {
    name: "Telefone com valor pequeno",
    data: {
      pixKey: "+5511999887766",
      receiverName: "José da Silva",
      receiverCity: "São José dos Campos",
      amount: 0.01,
      reference: "",
      description: "Pagamento mínimo"
    }
  }
];

testCases.forEach((testCase, index) => {
  console.log(`\n--- TESTE ${index + 1}: ${testCase.name} ---`);
  
  const originalBRCode = generateBRCodeOriginal(testCase.data);
  const fixedBRCode = generateBRCodeFixed(testCase.data);

  console.log("Dados de teste:");
  console.log(JSON.stringify(testCase.data, null, 2));
  console.log();

  console.log("BR Code Original (funcional):");
  console.log(originalBRCode);
  console.log("Tamanho:", originalBRCode.length);
  console.log();

  console.log("BR Code Demo Corrigido:");
  console.log(fixedBRCode);
  console.log("Tamanho:", fixedBRCode.length);
  console.log();

  const isEqual = originalBRCode === fixedBRCode;
  console.log(`São iguais: ${isEqual ? '✅' : '❌'} ${isEqual}`);
  
  if (!isEqual) {
    console.log("\n=== ANÁLISE DAS DIFERENÇAS ===");
    
    // Comparar caractere por caractere
    const minLength = Math.min(originalBRCode.length, fixedBRCode.length);
    let firstDifference = -1;
    
    for (let i = 0; i < minLength; i++) {
      if (originalBRCode[i] !== fixedBRCode[i]) {
        firstDifference = i;
        break;
      }
    }
    
    if (firstDifference !== -1) {
      console.log(`Primeira diferença na posição ${firstDifference}:`);
      console.log(`Original: "${originalBRCode.substring(firstDifference - 5, firstDifference + 10)}"`);
      console.log(`Corrigido: "${fixedBRCode.substring(firstDifference - 5, firstDifference + 10)}"`);
      console.log(`           ${' '.repeat(5)}^`);
    }
    
    // Comparar CRCs
    const originalCRC = originalBRCode.slice(-4);
    const fixedCRC = fixedBRCode.slice(-4);
    
    console.log(`\nCRC Original: ${originalCRC}`);
    console.log(`CRC Corrigido: ${fixedCRC}`);
    console.log(`CRCs iguais: ${originalCRC === fixedCRC}`);
  }
});

console.log("\n=== RESUMO ===");
console.log("✅ Correção aplicada no demo.html:");
console.log("   - Linha 675: Corrigida formatação do campo de valor");
console.log("   - Linhas 693-694: Padronizadas para usar formatBRCodeField");
console.log("✅ Os BR Codes agora devem ser compatíveis com aplicativos bancários!");
