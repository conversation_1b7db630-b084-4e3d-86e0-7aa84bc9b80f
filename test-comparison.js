// Teste de comparação entre as duas implementações de geração de BR Code PIX

// ===== VERSÃO ORIGINAL (funcional) =====
function generateBRCodeOriginal(data) {
  const getValue = (tag, value) => {
    return `${tag}${value.length.toString().padStart(2, "0")}${value}`;
  };

  const formattedText = (text) => {
    return text
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, "");
  };

  const crcCompute = (data) => {
    let crc = 0xffff;
    const dataBytes = new TextEncoder().encode(data);

    for (const byte of dataBytes) {
      crc ^= (byte << 8);
      for (let i = 0; i < 8; i++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ 0x1021;
        } else {
          crc <<= 1;
        }
      }
    }

    return (crc & 0xffff).toString(16).toUpperCase().padStart(4, "0");
  };

  // Build account information
  const basePix = getValue("00", "br.gov.bcb.pix");
  let infoString = getValue("01", data.pixKey);

  if (data.description) {
    infoString += getValue("02", formattedText(data.description));
  }

  const accountInfo = getValue("26", basePix + infoString);

  // Build additional data field
  const txid = data.reference || "***";
  const additionalData = getValue("62", getValue("05", formattedText(txid)));

  // Build complete BR Code
  let resultString = getValue("00", "01"); // Payload Format Indicator
  resultString += getValue("01", "11"); // Point of Initiation Method (static)
  resultString += accountInfo;
  resultString += getValue("52", "0000"); // Merchant Category Code
  resultString += getValue("53", "986"); // Transaction Currency (BRL)

  if (data.amount > 0) {
    resultString += getValue("54", data.amount.toFixed(2));
  }

  resultString += getValue("58", "BR"); // Country Code
  resultString += getValue("59", formattedText(data.receiverName)); // Merchant Name
  resultString += getValue("60", formattedText(data.receiverCity)); // Merchant City
  resultString += additionalData;
  resultString += "6304"; // CRC placeholder

  const finalBRCode = resultString + crcCompute(resultString);

  return finalBRCode;
}

// ===== VERSÃO REFATORADA (com problema) =====
function generateBRCodeRefactored(data) {
  const createField = (tag, value) => {
    const length = value.length.toString().padStart(2, '0');
    return `${tag}${length}${value}`;
  };

  const formatText = (text) => {
    return text
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '') // Keep only allowed characters
      .trim();
  };

  const calculateCRC16 = (data) => {
    let crc = 0xFFFF;
    const dataBytes = new TextEncoder().encode(data);

    for (const byte of dataBytes) {
      crc ^= byte << 8;
      for (let i = 0; i < 8; i++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ 0x1021;
        } else {
          crc <<= 1;
        }
      }
    }

    return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, '0');
  };

  const createAccountInformation = (data) => {
    const basePix = createField('00', 'br.gov.bcb.pix');
    let infoString = createField('01', data.pixKey);

    if (data.description) {
      infoString += createField('02', formatText(data.description));
    }

    return createField('26', basePix + infoString);
  };

  const createAdditionalDataField = (data) => {
    const txid = data.reference ? formatText(data.reference) : '***';
    return createField('62', createField('05', txid));
  };

  const fields = [
    createField('00', '01'),
    createField('01', '11'),
    createAccountInformation(data),
    createField('52', '0000'),
    createField('53', '986')
  ];

  // Add amount only if not zero
  if (data.amount > 0) {
    fields.push(createField('54', data.amount.toFixed(2)));
  }

  fields.push(
    createField('58', 'BR'),
    createField('59', formatText(data.receiverName)),
    createField('60', formatText(data.receiverCity)),
    createAdditionalDataField(data),
    '6304' // CRC placeholder
  );

  const brCodeWithoutCRC = fields.join('');
  const crc = calculateCRC16(brCodeWithoutCRC);
  
  return brCodeWithoutCRC + crc;
}

// ===== TESTE DE COMPARAÇÃO =====
const testCases = [
  {
    name: "CPF com valor",
    data: {
      pixKey: "***********",
      receiverName: "João Silva",
      receiverCity: "São Paulo",
      amount: 150.75,
      reference: "TEST001",
      description: "Pagamento teste"
    }
  },
  {
    name: "Email sem valor",
    data: {
      pixKey: "<EMAIL>",
      receiverName: "Maria Santos",
      receiverCity: "Rio de Janeiro",
      amount: 0,
      reference: "REF123",
      description: ""
    }
  },
  {
    name: "Telefone com acentos",
    data: {
      pixKey: "+5511999887766",
      receiverName: "José da Silva",
      receiverCity: "São José dos Campos",
      amount: 25.50,
      reference: "",
      description: "Pagamento com acentuação"
    }
  },
  {
    name: "Chave aleatória",
    data: {
      pixKey: "123e4567-e89b-12d3-a456-426614174000",
      receiverName: "Ana Costa",
      receiverCity: "Brasília",
      amount: 1000.00,
      reference: "INVOICE-2024-001",
      description: "Fatura mensal"
    }
  }
];

console.log("=== TESTE DE COMPARAÇÃO BR CODE PIX ===\n");

testCases.forEach((testCase, index) => {
  console.log(`\n--- TESTE ${index + 1}: ${testCase.name} ---`);

  const originalBRCode = generateBRCodeOriginal(testCase.data);
  const refactoredBRCode = generateBRCodeRefactored(testCase.data);

  console.log("Dados de teste:");
  console.log(JSON.stringify(testCase.data, null, 2));
  console.log();

  console.log("BR Code Original (funcional):");
  console.log(originalBRCode);
  console.log("Tamanho:", originalBRCode.length);
  console.log();

  console.log("BR Code Refatorado (com problema):");
  console.log(refactoredBRCode);
  console.log("Tamanho:", refactoredBRCode.length);
  console.log();

  console.log("São iguais?", originalBRCode === refactoredBRCode);

  if (originalBRCode !== refactoredBRCode) {
    console.log("\n=== ANÁLISE DAS DIFERENÇAS ===");

    // Comparar caractere por caractere
    const minLength = Math.min(originalBRCode.length, refactoredBRCode.length);
    let firstDifference = -1;

    for (let i = 0; i < minLength; i++) {
      if (originalBRCode[i] !== refactoredBRCode[i]) {
        firstDifference = i;
        break;
      }
    }

    if (firstDifference !== -1) {
      console.log(`Primeira diferença na posição ${firstDifference}:`);
      console.log(`Original: "${originalBRCode.substring(firstDifference - 5, firstDifference + 10)}"`);
      console.log(`Refatorado: "${refactoredBRCode.substring(firstDifference - 5, firstDifference + 10)}"`);
      console.log(`           ${' '.repeat(5)}^`);
    }

    // Comparar CRCs
    const originalCRC = originalBRCode.slice(-4);
    const refactoredCRC = refactoredBRCode.slice(-4);

    console.log(`\nCRC Original: ${originalCRC}`);
    console.log(`CRC Refatorado: ${refactoredCRC}`);
    console.log(`CRCs iguais: ${originalCRC === refactoredCRC}`);
  }
});
