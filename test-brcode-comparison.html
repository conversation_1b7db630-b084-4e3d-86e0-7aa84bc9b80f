<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Comparação BR Code</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .app-section {
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
        }
        .app-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            word-break: break-all;
        }
        .comparison-result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
        }
        .match {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .no-match {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🔍 Teste de Comparação BR Code PIX</h1>
    <p>Este teste compara os BR Codes gerados pela aplicação original e refatorada para verificar compatibilidade.</p>

    <div class="form-group">
        <label>Tipo de Chave PIX:</label>
        <select id="keyType">
            <option value="cpf">CPF</option>
            <option value="phone">Telefone</option>
            <option value="email">Email</option>
            <option value="random">Chave Aleatória</option>
        </select>
    </div>

    <div class="form-group">
        <label>Chave PIX:</label>
        <input type="text" id="pixKey" value="12345678901" placeholder="Digite a chave PIX">
    </div>

    <div class="form-group">
        <label>Nome do Recebedor:</label>
        <input type="text" id="receiverName" value="João Silva" placeholder="Nome do recebedor">
    </div>

    <div class="form-group">
        <label>Cidade:</label>
        <input type="text" id="receiverCity" value="São Paulo" placeholder="Cidade">
    </div>

    <div class="form-group">
        <label>Valor (opcional):</label>
        <input type="number" id="amount" value="10.50" step="0.01" placeholder="0.00">
    </div>

    <div class="form-group">
        <label>Referência (opcional):</label>
        <input type="text" id="reference" value="TESTE123" placeholder="Referência">
    </div>

    <div class="form-group">
        <label>Descrição (opcional):</label>
        <input type="text" id="description" value="Pagamento teste" placeholder="Descrição">
    </div>

    <button onclick="compareGenerators()">🔄 Comparar BR Codes</button>

    <div class="comparison">
        <div class="app-section">
            <h3>📱 Aplicação Original (Porta 8080)</h3>
            <div id="original-result" class="result">Clique em "Comparar BR Codes" para gerar</div>
        </div>

        <div class="app-section">
            <h3>🏗️ Aplicação Refatorada (Porta 3001)</h3>
            <div id="refactored-result" class="result">Clique em "Comparar BR Codes" para gerar</div>
        </div>
    </div>

    <div id="comparison-result" class="comparison-result" style="display: none;"></div>

    <script>
        // Função para gerar BR Code usando a lógica da aplicação original
        function generateOriginalBRCode(data) {
            const getValue = (tag, value) => {
                return `${tag}${value.length.toString().padStart(2, "0")}${value}`;
            };

            const formattedText = (text) => {
                return text
                    .normalize("NFD")
                    .replace(/[\u0300-\u036f]/g, "")
                    .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, "");
            };

            const crcCompute = (data) => {
                let crc = 0xffff;
                const dataBytes = new TextEncoder().encode(data);

                for (const byte of dataBytes) {
                    crc ^= byte << 8;
                    for (let i = 0; i < 8; i++) {
                        if (crc & 0x8000) {
                            crc = (crc << 1) ^ 0x1021;
                        } else {
                            crc <<= 1;
                        }
                    }
                }

                return (crc & 0xffff).toString(16).toUpperCase().padStart(4, "0");
            };

            // Build account information
            const basePix = getValue("00", "br.gov.bcb.pix");
            let infoString = getValue("01", data.pixKey);

            if (data.description) {
                infoString += getValue("02", formattedText(data.description));
            }

            const accountInfo = getValue("26", basePix + infoString);

            // Build additional data field
            const txid = data.reference || "***";
            const additionalData = getValue("62", getValue("05", formattedText(txid)));

            // Build complete BR Code
            let resultString = getValue("00", "01"); // Payload Format Indicator
            resultString += getValue("01", "11"); // Point of Initiation Method (static)
            resultString += accountInfo;
            resultString += getValue("52", "0000"); // Merchant Category Code
            resultString += getValue("53", "986"); // Transaction Currency (BRL)

            if (data.amount > 0) {
                resultString += getValue("54", data.amount.toFixed(2));
            }

            resultString += getValue("58", "BR"); // Country Code
            resultString += getValue("59", formattedText(data.receiverName)); // Merchant Name
            resultString += getValue("60", formattedText(data.receiverCity)); // Merchant City
            resultString += additionalData;
            resultString += "6304"; // CRC placeholder

            const finalBRCode = resultString + crcCompute(resultString);

            return finalBRCode;
        }

        // Função para gerar BR Code usando a lógica da aplicação refatorada
        function generateRefactoredBRCode(data) {
            const createField = (tag, value) => {
                const length = value.length.toString().padStart(2, '0');
                return `${tag}${length}${value}`;
            };

            const formatText = (text) => {
                return text
                    .normalize('NFD')
                    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
                    .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '') // Keep only allowed characters
                    .trim();
            };

            const calculateCRC16 = (data) => {
                let crc = 0xFFFF;
                const dataBytes = new TextEncoder().encode(data);

                for (const byte of dataBytes) {
                    crc ^= byte << 8;
                    for (let i = 0; i < 8; i++) {
                        if (crc & 0x8000) {
                            crc = (crc << 1) ^ 0x1021;
                        } else {
                            crc <<= 1;
                        }
                    }
                }

                return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, '0');
            };

            const createAccountInformation = (data) => {
                const basePix = createField('00', 'br.gov.bcb.pix');
                let infoString = createField('01', data.pixKey);

                if (data.description) {
                    infoString += createField('02', formatText(data.description));
                }

                return createField('26', basePix + infoString);
            };

            const createAdditionalDataField = (data) => {
                const txid = data.reference ? formatText(data.reference) : '***';
                return createField('62', createField('05', txid));
            };

            const fields = [
                createField('00', '01'), // PAYLOAD_FORMAT_INDICATOR
                createField('01', '11'), // POINT_OF_INITIATION_STATIC
                createAccountInformation(data),
                createField('52', '0000'), // MERCHANT_CATEGORY_CODE
                createField('53', '986')   // CURRENCY_CODE (BRL)
            ];

            // Add amount only if not zero
            if (data.amount > 0) {
                fields.push(createField('54', data.amount.toFixed(2)));
            }

            fields.push(
                createField('58', 'BR'), // COUNTRY_CODE
                createField('59', formatText(data.receiverName)),
                createField('60', formatText(data.receiverCity)),
                createAdditionalDataField(data),
                '6304' // CRC placeholder
            );

            const brCodeWithoutCRC = fields.join('');
            const crc = calculateCRC16(brCodeWithoutCRC);
            
            return brCodeWithoutCRC + crc;
        }

        function getFormData() {
            return {
                keyType: document.getElementById('keyType').value,
                pixKey: document.getElementById('pixKey').value.trim(),
                receiverName: document.getElementById('receiverName').value.trim(),
                receiverCity: document.getElementById('receiverCity').value.trim(),
                amount: parseFloat(document.getElementById('amount').value) || 0,
                reference: document.getElementById('reference').value.trim(),
                description: document.getElementById('description').value.trim()
            };
        }

        function compareGenerators() {
            const data = getFormData();
            
            // Validação básica
            if (!data.pixKey || !data.receiverName || !data.receiverCity) {
                alert('Por favor, preencha pelo menos a chave PIX, nome e cidade.');
                return;
            }

            try {
                const originalBRCode = generateOriginalBRCode(data);
                const refactoredBRCode = generateRefactoredBRCode(data);

                document.getElementById('original-result').innerHTML = `
                    <strong>BR Code:</strong><br>
                    <code>${originalBRCode}</code><br><br>
                    <strong>Tamanho:</strong> ${originalBRCode.length} caracteres
                `;

                document.getElementById('refactored-result').innerHTML = `
                    <strong>BR Code:</strong><br>
                    <code>${refactoredBRCode}</code><br><br>
                    <strong>Tamanho:</strong> ${refactoredBRCode.length} caracteres
                `;

                const comparisonDiv = document.getElementById('comparison-result');
                comparisonDiv.style.display = 'block';

                if (originalBRCode === refactoredBRCode) {
                    comparisonDiv.className = 'comparison-result match';
                    comparisonDiv.innerHTML = '✅ SUCESSO: Os BR Codes são IDÊNTICOS!<br>As duas aplicações geram códigos compatíveis.';
                } else {
                    comparisonDiv.className = 'comparison-result no-match';
                    comparisonDiv.innerHTML = '❌ PROBLEMA: Os BR Codes são DIFERENTES!<br>Há incompatibilidade entre as aplicações.';
                    
                    // Mostrar diferenças
                    console.log('Original:', originalBRCode);
                    console.log('Refatorada:', refactoredBRCode);
                    console.log('Diferenças encontradas');
                }

            } catch (error) {
                alert('Erro ao gerar BR Codes: ' + error.message);
                console.error(error);
            }
        }

        // Preencher dados de exemplo ao carregar
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Teste de comparação BR Code carregado');
        });
    </script>
</body>
</html>
