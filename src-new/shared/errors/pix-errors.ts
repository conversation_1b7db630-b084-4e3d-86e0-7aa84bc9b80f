/**
 * Custom error classes for PIX operations
 */

export class PixError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'PixError';
  }
}

export class ValidationError extends PixError {
  constructor(field: string, message: string) {
    super(`Validation error in field "${field}": ${message}`);
    this.name = 'ValidationError';
  }
}

export class QRGenerationError extends PixError {
  constructor(message: string, originalError?: Error) {
    super(`QR Code generation failed: ${message}`);
    this.name = 'QRGenerationError';
    if (originalError && originalError.stack) {
      this.stack = originalError.stack;
    }
  }
}

export class BRCodeGenerationError extends PixError {
  constructor(message: string) {
    super(`BR Code generation failed: ${message}`);
    this.name = 'BRCodeGenerationError';
  }
}