/**
 * Performance Logger
 * Centralized performance monitoring and logging
 */

export interface PerformanceMetric {
  name: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface PerformanceStats {
  count: number;
  totalDuration: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  percentile95: number;
  lastExecuted: number;
}

export class PerformanceLogger {
  private static instance: PerformanceLogger;
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private readonly maxMetricsPerName: number;
  private readonly enableLogging: boolean;

  private constructor(maxMetricsPerName = 1000, enableLogging = true) {
    this.maxMetricsPerName = maxMetricsPerName;
    this.enableLogging = enableLogging;
  }

  static getInstance(maxMetricsPerName = 1000, enableLogging = true): PerformanceLogger {
    if (!PerformanceLogger.instance) {
      PerformanceLogger.instance = new PerformanceLogger(maxMetricsPerName, enableLogging);
    }
    return PerformanceLogger.instance;
  }

  startMeasurement(name: string, metadata?: Record<string, any>): () => void {
    if (!this.enableLogging) {
      return () => {}; // No-op if logging is disabled
    }

    const startTime = performance.now();
    const timestamp = Date.now();

    return (): void => {
      const duration = performance.now() - startTime;
      const metric: any = {
        name,
        duration,
        timestamp
      };

      if (metadata) {
        metric.metadata = metadata;
      }

      this.recordMetric(metric);
    };
  }

  async measureAsync<T>(
    name: string,
    asyncFunction: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    if (!this.enableLogging) {
      return asyncFunction();
    }

    const endMeasurement = this.startMeasurement(name, metadata);
    try {
      const result = await asyncFunction();
      return result;
    } finally {
      endMeasurement();
    }
  }

  measure<T>(
    name: string,
    syncFunction: () => T,
    metadata?: Record<string, any>
  ): T {
    if (!this.enableLogging) {
      return syncFunction();
    }

    const endMeasurement = this.startMeasurement(name, metadata);
    try {
      return syncFunction();
    } finally {
      endMeasurement();
    }
  }

  private recordMetric(metric: PerformanceMetric): void {
    if (!this.metrics.has(metric.name)) {
      this.metrics.set(metric.name, []);
    }

    const metrics = this.metrics.get(metric.name)!;
    metrics.push(metric);

    // Keep only the most recent metrics to prevent memory leaks
    if (metrics.length > this.maxMetricsPerName) {
      metrics.splice(0, metrics.length - this.maxMetricsPerName);
    }

    // Log slow operations
    if (metric.duration > 100) { // More than 100ms
      console.warn(`🐌 Slow operation detected: ${metric.name} took ${metric.duration.toFixed(2)}ms`);
    }
  }

  getStats(name: string): PerformanceStats | null {
    const metrics = this.metrics.get(name);
    if (!metrics || metrics.length === 0) {
      return null;
    }

    const durations = metrics.map(m => m.duration).sort((a, b) => a - b);
    const totalDuration = durations.reduce((sum, duration) => sum + duration, 0);

    return {
      count: metrics.length,
      totalDuration,
      averageDuration: totalDuration / metrics.length,
      minDuration: durations[0] || 0,
      maxDuration: durations[durations.length - 1] || 0,
      percentile95: durations[Math.floor(durations.length * 0.95)] || durations[durations.length - 1] || 0,
      lastExecuted: Math.max(...metrics.map(m => m.timestamp))
    };
  }

  getAllStats(): Record<string, PerformanceStats> {
    const result: Record<string, PerformanceStats> = {};
    for (const name of this.metrics.keys()) {
      const stats = this.getStats(name);
      if (stats) {
        result[name] = stats;
      }
    }
    return result;
  }

  getMetrics(name: string): PerformanceMetric[] | null {
    return this.metrics.get(name) || null;
  }

  generateReport(): string {
    const stats = this.getAllStats();
    const sortedEntries = Object.entries(stats)
      .sort(([, a], [, b]) => b.averageDuration - a.averageDuration);

    let report = '📊 Performance Report\n';
    report += '=' .repeat(50) + '\n\n';

    if (sortedEntries.length === 0) {
      report += 'No performance metrics recorded.\n';
      return report;
    }

    report += `${'Operation'.padEnd(25)} | ${'Count'.padStart(6)} | ${'Avg (ms)'.padStart(8)} | ${'95th (ms)'.padStart(9)} | ${'Max (ms)'.padStart(8)}\n`;
    report += '-'.repeat(65) + '\n';

    for (const [name, stat] of sortedEntries) {
      report += `${name.padEnd(25)} | ${stat.count.toString().padStart(6)} | `;
      report += `${stat.averageDuration.toFixed(2).padStart(8)} | `;
      report += `${stat.percentile95.toFixed(2).padStart(9)} | `;
      report += `${stat.maxDuration.toFixed(2).padStart(8)}\n`;
    }

    report += '\n🎯 Performance Insights:\n';

    // Find the slowest operations
    const slowOperations = sortedEntries.filter(([, stat]) => stat.averageDuration > 10);
    if (slowOperations.length > 0) {
      report += `• Slowest operations (>10ms avg):\n`;
      slowOperations.slice(0, 3).forEach(([name, stat]) => {
        report += `  - ${name}: ${stat.averageDuration.toFixed(2)}ms avg\n`;
      });
    }

    // Find operations with high variance
    const highVarianceOps = sortedEntries.filter(([, stat]) => {
      const variance = stat.maxDuration - stat.minDuration;
      return variance > stat.averageDuration * 2; // High variance
    });

    if (highVarianceOps.length > 0) {
      report += `• High variance operations:\n`;
      highVarianceOps.slice(0, 3).forEach(([name, stat]) => {
        const variance = stat.maxDuration - stat.minDuration;
        report += `  - ${name}: ${variance.toFixed(2)}ms variance (${stat.minDuration.toFixed(2)}-${stat.maxDuration.toFixed(2)}ms)\n`;
      });
    }

    // Memory usage recommendations
    const totalOperations = Object.values(stats).reduce((sum, stat) => sum + stat.count, 0);
    report += `\n📈 Total operations monitored: ${totalOperations}\n`;
    report += `📊 Unique operation types: ${Object.keys(stats).length}\n`;

    return report;
  }

  clear(): void {
    this.metrics.clear();
  }

  clearMetrics(name: string): void {
    this.metrics.delete(name);
  }

  exportMetrics(): Record<string, PerformanceMetric[]> {
    const result: Record<string, PerformanceMetric[]> = {};
    for (const [name, metrics] of this.metrics.entries()) {
      result[name] = [...metrics]; // Create a copy
    }
    return result;
  }

  importMetrics(data: Record<string, PerformanceMetric[]>): void {
    for (const [name, metrics] of Object.entries(data)) {
      this.metrics.set(name, [...metrics]);
    }
  }

  // Integration with domain operations
  static measureDomainOperation<T>(
    operationName: string,
    operation: () => T,
    entityType?: string
  ): T {
    const logger = PerformanceLogger.getInstance();
    return logger.measure(
      `domain.${operationName}`,
      operation,
      entityType ? { entityType } : undefined
    );
  }

  static async measureApplicationOperation<T>(
    useCaseName: string,
    operation: () => Promise<T>,
    requestSize?: number
  ): Promise<T> {
    const logger = PerformanceLogger.getInstance();
    return logger.measureAsync(
      `application.${useCaseName}`,
      operation,
      requestSize ? { requestSize } : undefined
    );
  }

  static measureInfrastructureOperation<T>(
    operationName: string,
    operation: () => T,
    provider?: string
  ): T {
    const logger = PerformanceLogger.getInstance();
    return logger.measure(
      `infrastructure.${operationName}`,
      operation,
      provider ? { provider } : undefined
    );
  }
}

// Decorator for automatic performance measurement
export function measurePerformance(name?: string, includeParams = false) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const operationName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = function (...args: any[]) {
      const logger = PerformanceLogger.getInstance();
      const metadata = includeParams ? { paramCount: args.length } : undefined;

      if (originalMethod.constructor.name === 'AsyncFunction') {
        return logger.measureAsync(operationName, () => originalMethod.apply(this, args), metadata);
      } else {
        return logger.measure(operationName, () => originalMethod.apply(this, args), metadata);
      }
    };

    return descriptor;
  };
}

// React-like hook for performance monitoring in components
export function usePerformanceMonitoring(componentName: string) {
  const logger = PerformanceLogger.getInstance();

  const measureRender = (renderFunction: () => void) => {
    logger.measure(`render.${componentName}`, renderFunction);
  };

  const measureInteraction = (interactionName: string, interaction: () => void) => {
    logger.measure(`interaction.${componentName}.${interactionName}`, interaction);
  };

  const getComponentStats = () => {
    return {
      render: logger.getStats(`render.${componentName}`),
      interactions: Object.keys(logger.getAllStats())
        .filter(key => key.startsWith(`interaction.${componentName}.`))
        .reduce((acc, key) => {
          const interactionName = key.replace(`interaction.${componentName}.`, '');
          acc[interactionName] = logger.getStats(key);
          return acc;
        }, {} as Record<string, PerformanceStats | null>)
    };
  };

  return {
    measureRender,
    measureInteraction,
    getComponentStats
  };
}

// Global performance logger instance
export const performanceLogger = PerformanceLogger.getInstance();