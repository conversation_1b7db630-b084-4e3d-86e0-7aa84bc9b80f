/**
 * Enhanced QR Code Repository Implementation
 * Uses the new infrastructure layer with factory pattern
 */

import { QRCodeRepository } from '../../application/usecases/generate-pix-qrcode';
import { QRGenerationOptions } from '../../shared/types/pix-types';
import { QRGenerationError } from '../../shared/errors/pix-errors';
import { QRGeneratorFactory, QRGeneratorConfig } from '../qr-generators/qr-generator-factory';
import { FileOperationsService, DownloadOptions } from '../file-system/file-operations';

export interface EnhancedQRCodeRepository extends QRCodeRepository {
  downloadQRCode(dataUrl: string, options: DownloadOptions): Promise<void>;
  generateQRCodeWithImage(options: QRGenerationOptions, imageFile: File): Promise<string>;
  generateStandardQRCode(data: string): Promise<string>;
}

export class QRCodeRepositoryEnhanced implements EnhancedQRCodeRepository {
  private qrGenerator: QRGeneratorFactory;
  private fileService: FileOperationsService;

  constructor(config?: QRGeneratorConfig) {
    this.qrGenerator = new QRGeneratorFactory(config);
    this.fileService = new FileOperationsService();
  }

  async generateQRCode(options: QRGenerationOptions): Promise<string> {
    try {
      const result = await this.qrGenerator.generateQRCode(options);
      
      if (!result.dataUrl) {
        // If no dataUrl from provider, extract it from element
        result.dataUrl = await this.extractDataUrl(result.element);
      }
      
      return result.dataUrl;
    } catch (error) {
      throw new QRGenerationError(
        `Enhanced QR generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async generateZeroMarginQRCode(options: QRGenerationOptions): Promise<string> {
    try {
      const result = await this.qrGenerator.generateZeroMarginQRCode(options);
      
      if (!result.dataUrl) {
        result.dataUrl = await this.extractDataUrl(result.element);
      }
      
      return result.dataUrl;
    } catch (error) {
      throw new QRGenerationError(
        `Zero margin QR generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async generateStandardQRCode(data: string): Promise<string> {
    try {
      const result = await this.qrGenerator.generateStandardQRCode(data);
      
      if (!result.dataUrl) {
        result.dataUrl = await this.extractDataUrl(result.element);
      }
      
      return result.dataUrl;
    } catch (error) {
      throw new QRGenerationError(
        `Standard QR generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async generateQRCodeWithImage(options: QRGenerationOptions, imageFile: File): Promise<string> {
    try {
      // Validate image file first
      const validation = this.fileService.validateImageFile(imageFile);
      if (!validation.isValid) {
        throw new Error(validation.error || 'Invalid image file');
      }

      const result = await this.qrGenerator.generateWithCustomImage(options, imageFile);
      
      if (!result.dataUrl) {
        result.dataUrl = await this.extractDataUrl(result.element);
      }
      
      return result.dataUrl;
    } catch (error) {
      throw new QRGenerationError(
        `QR generation with image failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async downloadQRCode(dataUrl: string, options: DownloadOptions): Promise<void> {
    try {
      await this.fileService.downloadFile(dataUrl, options);
    } catch (error) {
      throw new QRGenerationError(
        `QR code download failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private async extractDataUrl(element: HTMLElement): Promise<string> {
    if (element.tagName.toLowerCase() === 'canvas') {
      const canvas = element as HTMLCanvasElement;
      return canvas.toDataURL('image/png');
    }

    if (element.tagName.toLowerCase() === 'svg') {
      const svg = element as unknown as SVGElement;
      const svgRect = svg.getBoundingClientRect();
      return await this.fileService.svgToPng(svg, svgRect.width, svgRect.height);
    }

    if (element.tagName.toLowerCase() === 'img') {
      const img = element as HTMLImageElement;
      return img.src;
    }

    throw new Error('Cannot extract data URL from element type: ' + element.tagName);
  }

  // Additional utility methods
  async getQRCodeAsBlob(options: QRGenerationOptions, format: string = 'image/png'): Promise<Blob> {
    const dataUrl = await this.generateQRCode(options);
    const response = await fetch(dataUrl);
    return await response.blob();
  }

  async getQRCodeElement(options: QRGenerationOptions): Promise<HTMLElement> {
    const result = await this.qrGenerator.generateQRCode(options);
    return result.element;
  }

  getProviderInfo(): { current: string; available: string[] } {
    // This would be implemented to return info about available providers
    return {
      current: 'auto-selected',
      available: ['qr-code-styling', 'external-api']
    };
  }
}

// Factory functions for different configurations
export function createEnhancedQRCodeRepository(config?: QRGeneratorConfig): QRCodeRepositoryEnhanced {
  return new QRCodeRepositoryEnhanced(config);
}

export function createQRCodeRepositoryWithStyling(): QRCodeRepositoryEnhanced {
  return new QRCodeRepositoryEnhanced({
    preferredProvider: 'qr-code-styling',
    fallbackEnabled: true
  });
}

export function createQRCodeRepositoryWithExternalAPI(): QRCodeRepositoryEnhanced {
  return new QRCodeRepositoryEnhanced({
    preferredProvider: 'external-api',
    fallbackEnabled: false
  });
}