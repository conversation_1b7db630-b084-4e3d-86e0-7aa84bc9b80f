{"name": "qr-code-styling-clean", "version": "2.0.0", "description": "QR Code Styling with Clean Architecture - TypeScript refactored version", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc && npm run copy-demo", "build:watch": "tsc --watch", "copy-demo": "cp demo.html dist/index.html", "lint": "eslint . --ext .ts --fix", "format": "prettier --write \"**/*.{ts,js,json,md}\"", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "dev": "npm run clean && npm run build:watch", "serve": "npm run build && http-server dist -p 3000 -c-1 --cors", "start": "npm run build && node dist/dev-server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:performance": "ts-node performance/performance-tests.ts"}, "devDependencies": {"@types/jest": "^29.5.3", "@types/node": "^20.4.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "http-server": "^14.1.1", "jest": "^29.6.2", "jest-environment-jsdom": "^29.6.2", "prettier": "^3.0.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "keywords": ["qr-code", "pix", "typescript", "clean-architecture", "domain-driven-design"], "author": "Refactored for Clean Architecture", "license": "MIT"}