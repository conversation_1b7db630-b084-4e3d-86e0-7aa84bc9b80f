/**
 * Money Value Object
 * Handles monetary values with proper validation and formatting
 */

import { ValidationError } from '../../shared/errors/pix-errors';

export class Money {
  private static readonly MAX_DIGITS = 13; // Including decimals

  private constructor(private readonly _amount: number) {}

  static create(amount: number): Money {
    if (amount < 0) {
      throw new ValidationError('amount', 'Amount cannot be negative');
    }

    const formattedValue = amount.toFixed(2);
    if (formattedValue.length > Money.MAX_DIGITS) {
      throw new ValidationError('amount', `Amount exceeds maximum of ${Money.MAX_DIGITS} characters`);
    }

    return new Money(amount);
  }

  static zero(): Money {
    return new Money(0);
  }

  get amount(): number {
    return this._amount;
  }

  getValue(): number {
    return this._amount;
  }

  /**
   * Returns Brazilian currency formatted string
   */
  toBrazilianCurrency(): string {
    return `R$ ${this._amount.toFixed(2).replace('.', ',')}`;
  }

  /**
   * Returns decimal formatted string for BR Code
   */
  toDecimalString(): string {
    return this._amount.toFixed(2);
  }

  isZero(): boolean {
    return this._amount === 0;
  }

  equals(other: Money): boolean {
    return Math.abs(this._amount - other._amount) < 0.001; // Handle floating point precision
  }

  add(other: Money): Money {
    return Money.create(this._amount + other._amount);
  }

  subtract(other: Money): Money {
    return Money.create(this._amount - other._amount);
  }
}