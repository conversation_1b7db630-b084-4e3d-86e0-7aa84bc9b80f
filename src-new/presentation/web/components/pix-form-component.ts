/**
 * PIX Form Component
 * Manages form state, validation, and user interactions
 */

import { Pix<PERSON>ey } from '../../../domain/value-objects/pix-key';
import { Money } from '../../../domain/value-objects/money';
import { PixTransaction } from '../../../domain/entities/pix-transaction';
import { ValidationError } from '../../../shared/errors/pix-errors';
import { PixKeyType, PixFormData } from '../../../shared/types/pix-types';

export interface PixFormElements {
  form: HTMLFormElement;
  keyTypeSelect: HTMLSelectElement;
  pixKeyInput: HTMLInputElement;
  receiverNameInput: HTMLInputElement;
  receiverCityInput: HTMLInputElement;
  amountInput: HTMLInputElement;
  referenceInput: HTMLInputElement;
  descriptionInput: HTMLTextAreaElement;
  generateBtn: HTMLButtonElement;
  keyValidation: HTMLElement;
}

export interface PixFormCallbacks {
  onFormSubmit: (data: PixFormData) => Promise<void>;
  onKeyValidation: (isValid: boolean, message: string) => void;
  onFormChange: (data: Partial<PixFormData>) => void;
}

export class PixFormComponent {
  private elements: PixFormElements;
  private callbacks: PixFormCallbacks;
  private formData: Partial<PixFormData> = {};

  constructor(elements: PixFormElements, callbacks: PixFormCallbacks) {
    this.elements = elements;
    this.callbacks = callbacks;
    this.setupEventListeners();
    this.setupMasks();
    this.setupValidation();
  }

  private setupEventListeners(): void {
    // Form submission
    this.elements.form.addEventListener('submit', (e) => this.handleFormSubmit(e));

    // Key type change
    this.elements.keyTypeSelect.addEventListener('change', () => this.handleKeyTypeChange());

    // PIX key input validation
    this.elements.pixKeyInput.addEventListener('input', () => this.validatePixKey());
    this.elements.pixKeyInput.addEventListener('blur', () => this.validatePixKey());

    // Character counters
    this.elements.receiverNameInput.addEventListener('input', () => 
      this.updateCharCounter(this.elements.receiverNameInput, 25)
    );
    this.elements.receiverCityInput.addEventListener('input', () => 
      this.updateCharCounter(this.elements.receiverCityInput, 15)
    );

    // Form changes
    this.elements.form.addEventListener('input', () => this.handleFormChange());
  }

  private setupMasks(): void {
    // Currency mask for amount input
    this.elements.amountInput.addEventListener('input', (e) => {
      const target = e.target as HTMLInputElement;
      let value = target.value.replace(/\\D/g, '');
      
      if (value.length === 0) {
        target.value = '';
        return;
      }

      const numericValue = (parseInt(value) / 100).toFixed(2);
      target.value = 'R$ ' + numericValue.replace('.', ',');
    });

    // Reference input: only alphanumeric
    this.elements.referenceInput.addEventListener('input', (e) => {
      const target = e.target as HTMLInputElement;
      target.value = target.value.replace(/[^A-Za-z0-9]/g, '');
    });
  }

  private setupValidation(): void {
    this.handleKeyTypeChange();
  }

  private handleKeyTypeChange(): void {
    const keyType = this.elements.keyTypeSelect.value as PixKeyType;
    const pixKeyInput = this.elements.pixKeyInput;

    // Clear previous value and validation
    pixKeyInput.value = '';
    this.clearValidation();

    // Update placeholder and input type based on key type
    switch (keyType) {
      case 'cpf':
        pixKeyInput.placeholder = '000.000.000-00';
        pixKeyInput.type = 'text';
        pixKeyInput.maxLength = 14;
        this.setupCPFMask();
        break;
      case 'phone':
        pixKeyInput.placeholder = '(11) 99999-9999';
        pixKeyInput.type = 'tel';
        pixKeyInput.maxLength = 15;
        this.setupPhoneMask();
        break;
      case 'email':
        pixKeyInput.placeholder = '<EMAIL>';
        pixKeyInput.type = 'email';
        pixKeyInput.maxLength = 50;
        this.removeMasks();
        break;
      case 'random':
        pixKeyInput.placeholder = 'chave-aleatoria-uuid';
        pixKeyInput.type = 'text';
        pixKeyInput.maxLength = 50;
        this.removeMasks();
        break;
    }

    this.formData.keyType = keyType;
    this.callbacks.onFormChange(this.formData);
  }

  private setupCPFMask(): void {
    this.removeMasks();
    this.cpfMaskHandler = (e: Event) => {
      const target = e.target as HTMLInputElement;
      let value = target.value.replace(/\\D/g, '');
      value = value.replace(/(\\d{3})(\\d)/, '$1.$2');
      value = value.replace(/(\\d{3})(\\d)/, '$1.$2');
      value = value.replace(/(\\d{3})(\\d{1,2})$/, '$1-$2');
      target.value = value;
    };
    this.elements.pixKeyInput.addEventListener('input', this.cpfMaskHandler);
  }

  private setupPhoneMask(): void {
    this.removeMasks();
    this.phoneMaskHandler = (e: Event) => {
      const target = e.target as HTMLInputElement;
      let value = target.value.replace(/\\D/g, '');
      
      if (value.length <= 10) {
        value = value.replace(/(\\d{2})(\\d)/, '($1) $2');
        value = value.replace(/(\\d{4})(\\d)/, '$1-$2');
      } else {
        value = value.replace(/(\\d{2})(\\d)/, '($1) $2');
        value = value.replace(/(\\d{5})(\\d)/, '$1-$2');
      }
      target.value = value;
    };
    this.elements.pixKeyInput.addEventListener('input', this.phoneMaskHandler);
  }

  private cpfMaskHandler?: (e: Event) => void;
  private phoneMaskHandler?: (e: Event) => void;

  private removeMasks(): void {
    if (this.cpfMaskHandler) {
      this.elements.pixKeyInput.removeEventListener('input', this.cpfMaskHandler);
    }
    if (this.phoneMaskHandler) {
      this.elements.pixKeyInput.removeEventListener('input', this.phoneMaskHandler);
    }
  }

  private validatePixKey(): void {
    const keyType = this.elements.keyTypeSelect.value as PixKeyType;
    const value = this.elements.pixKeyInput.value.trim();

    if (!value) {
      this.clearValidation();
      return;
    }

    try {
      PixKey.create(value, keyType);
      this.showValidation(true, this.getSuccessMessage(keyType));
      this.callbacks.onKeyValidation(true, this.getSuccessMessage(keyType));
    } catch (error) {
      if (error instanceof ValidationError) {
        this.showValidation(false, this.getErrorMessage(keyType));
        this.callbacks.onKeyValidation(false, this.getErrorMessage(keyType));
      }
    }
  }

  private showValidation(isValid: boolean, message: string): void {
    this.elements.keyValidation.textContent = message;
    this.elements.keyValidation.className = `validation-message ${isValid ? 'success' : 'error'}`;
  }

  private clearValidation(): void {
    this.elements.keyValidation.textContent = '';
    this.elements.keyValidation.className = 'validation-message';
  }

  private getSuccessMessage(keyType: PixKeyType): string {
    const messages: Record<PixKeyType, string> = {
      cpf: 'CPF válido',
      phone: 'Telefone válido',
      email: 'Email válido',
      random: 'Chave válida'
    };
    return messages[keyType];
  }

  private getErrorMessage(keyType: PixKeyType): string {
    const messages: Record<PixKeyType, string> = {
      cpf: 'CPF inválido',
      phone: 'Telefone inválido',
      email: 'Email inválido',
      random: 'Chave deve ter pelo menos 10 caracteres'
    };
    return messages[keyType];
  }

  private updateCharCounter(input: HTMLInputElement, maxLength: number): void {
    const counter = input.parentElement?.querySelector('.char-counter');
    if (counter) {
      counter.textContent = `${input.value.length}/${maxLength} caracteres`;
    }
  }

  private handleFormChange(): void {
    this.formData = {
      keyType: this.elements.keyTypeSelect.value as PixKeyType,
      pixKey: this.elements.pixKeyInput.value,
      receiverName: this.elements.receiverNameInput.value,
      receiverCity: this.elements.receiverCityInput.value,
      amount: this.parseAmount(this.elements.amountInput.value),
      reference: this.elements.referenceInput.value,
      description: this.elements.descriptionInput.value
    };

    this.callbacks.onFormChange(this.formData);
  }

  private async handleFormSubmit(e: Event): Promise<void> {
    e.preventDefault();

    if (!this.validateForm()) {
      return;
    }

    const formData = this.getFormData();
    await this.callbacks.onFormSubmit(formData);
  }

  private validateForm(): boolean {
    const requiredFields = [
      { element: this.elements.pixKeyInput, name: 'Chave PIX' },
      { element: this.elements.receiverNameInput, name: 'Nome do recebedor' },
      { element: this.elements.receiverCityInput, name: 'Cidade do recebedor' }
    ];

    for (const field of requiredFields) {
      if (!field.element.value.trim()) {
        this.showFieldError(field.element, `O campo "${field.name}" é obrigatório.`);
        return false;
      }
    }

    // Validate PIX key using domain validation
    try {
      const keyType = this.elements.keyTypeSelect.value as PixKeyType;
      PixKey.create(this.elements.pixKeyInput.value, keyType);
    } catch (error) {
      this.showFieldError(this.elements.pixKeyInput, 'Por favor, insira uma chave PIX válida.');
      return false;
    }

    return true;
  }

  private showFieldError(field: HTMLInputElement, message: string): void {
    // This would show field-specific error message
    // Implementation depends on UI framework
    field.focus();
    console.error(message);
  }


  private parseAmount(amountStr: string): number {
    if (!amountStr.trim()) return 0;
    
    const cleanValue = amountStr.replace(/[^\\d,]/g, '').replace(',', '.');
    return parseFloat(cleanValue) || 0;
  }

  // Public methods for external control
  public setLoading(loading: boolean): void {
    this.elements.generateBtn.disabled = loading;
    this.elements.generateBtn.classList.toggle('loading', loading);
  }

  public reset(): void {
    this.elements.form.reset();
    this.clearValidation();
    this.formData = {};
  }

  public getFormData(): PixFormData {
    return {
      keyType: this.elements.keyTypeSelect.value as PixKeyType,
      pixKey: this.elements.pixKeyInput.value.trim(),
      receiverName: this.elements.receiverNameInput.value.trim(),
      receiverCity: this.elements.receiverCityInput.value.trim(),
      amount: this.parseAmount(this.elements.amountInput.value),
      reference: this.elements.referenceInput.value.trim(),
      description: this.elements.descriptionInput.value.trim()
    };
  }
}