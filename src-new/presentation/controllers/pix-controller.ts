/**
 * PIX Controller
 * Handles HTTP requests for PIX QR code generation
 */

import { Pix<PERSON>ey } from '../../domain/value-objects/pix-key';
import { Money } from '../../domain/value-objects/money';
import { PixTransaction } from '../../domain/entities/pix-transaction';
import { GeneratePixQRCodeUseCase, GeneratePixQRCodeRequest } from '../../application/usecases/generate-pix-qrcode';
import { ValidationError, PixError } from '../../shared/errors/pix-errors';
import { PixKeyType, PixFormData } from '../../shared/types/pix-types';

export interface PixControllerRequest {
  keyType: PixKeyType;
  pixKey: string;
  receiverName: string;
  receiverCity: string;
  amount?: number;
  reference?: string;
  description?: string;
  customization?: {
    dotsType: string;
    dotsColor: string;
    cornerSquareType: string;
    cornerSquareColor: string;
    cornerDotType: string;
    cornerDotColor: string;
    backgroundColor: string;
    qrSize: number;
    imageSize: number;
    imageMargin: number;
    hideBackgroundDots: boolean;
  };
}

export interface PixControllerResponse {
  success: boolean;
  data?: {
    brCode: string;
    qrCodeDataUrl?: string;
    transactionDetails: Array<{ label: string; value: string }>;
  };
  error?: {
    message: string;
    code: string;
    field?: string;
  };
}

export class PixController {
  constructor(private generatePixQRCodeUseCase: GeneratePixQRCodeUseCase) {}

  async generateQRCode(request: PixControllerRequest): Promise<PixControllerResponse> {
    try {
      // Validate and create domain objects
      const pixKey = PixKey.create(request.pixKey, request.keyType);
      const amount = Money.create(request.amount || 0);
      
      const transactionParams: {
        pixKey: PixKey;
        receiverName: string;
        receiverCity: string;
        amount: Money;
        reference?: string;
        description?: string;
      } = {
        pixKey,
        receiverName: request.receiverName,
        receiverCity: request.receiverCity,
        amount
      };

      if (request.reference) {
        transactionParams.reference = request.reference;
      }

      if (request.description) {
        transactionParams.description = request.description;
      }

      const transaction = PixTransaction.create(transactionParams);

      // Execute use case
      const useCaseRequest: GeneratePixQRCodeRequest = {
        transaction,
        isDynamic: false
      };

      if (request.customization) {
        useCaseRequest.customization = request.customization;
      }

      const result = await this.generatePixQRCodeUseCase.execute(useCaseRequest);

      const responseData: {
        brCode: string;
        qrCodeDataUrl?: string;
        transactionDetails: Array<{ label: string; value: string }>;
      } = {
        brCode: result.brCode,
        transactionDetails: result.transaction.getDisplayDetails()
      };

      if (result.qrCodeDataUrl) {
        responseData.qrCodeDataUrl = result.qrCodeDataUrl;
      }

      return {
        success: true,
        data: responseData
      };

    } catch (error) {
      return this.handleError(error);
    }
  }

  async validatePixKey(pixKey: string, keyType: PixKeyType): Promise<{ isValid: boolean; message: string }> {
    try {
      PixKey.create(pixKey, keyType);
      return {
        isValid: true,
        message: this.getSuccessMessage(keyType)
      };
    } catch (error) {
      if (error instanceof ValidationError) {
        return {
          isValid: false,
          message: this.getErrorMessage(keyType)
        };
      }
      return {
        isValid: false,
        message: 'Erro interno de validação'
      };
    }
  }

  private handleError(error: unknown): PixControllerResponse {
    if (error instanceof ValidationError) {
      const errorResponse: {
        message: string;
        code: string;
        field?: string;
      } = {
        message: error.message,
        code: 'VALIDATION_ERROR'
      };

      const field = this.extractFieldFromError(error.message);
      if (field) {
        errorResponse.field = field;
      }

      return {
        success: false,
        error: errorResponse
      };
    }

    if (error instanceof PixError) {
      return {
        success: false,
        error: {
          message: error.message,
          code: error.name
        }
      };
    }

    // Generic error handling
    return {
      success: false,
      error: {
        message: 'Erro interno do servidor',
        code: 'INTERNAL_ERROR'
      }
    };
  }

  private extractFieldFromError(message: string): string | undefined {
    const fieldMatch = message.match(/field "([^"]+)"/);
    return fieldMatch ? fieldMatch[1] : undefined;
  }

  private getSuccessMessage(keyType: PixKeyType): string {
    const messages: Record<PixKeyType, string> = {
      cpf: 'CPF válido',
      phone: 'Telefone válido', 
      email: 'Email válido',
      random: 'Chave válida'
    };
    return messages[keyType];
  }

  private getErrorMessage(keyType: PixKeyType): string {
    const messages: Record<PixKeyType, string> = {
      cpf: 'CPF inválido',
      phone: 'Telefone inválido',
      email: 'Email inválido', 
      random: 'Chave deve ter pelo menos 10 caracteres'
    };
    return messages[keyType];
  }
}