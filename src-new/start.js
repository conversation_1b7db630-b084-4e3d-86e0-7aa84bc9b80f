#!/usr/bin/env node

/**
 * Quick Start Script
 * Builds and serves the demo for manual testing
 */

const { execSync, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const net = require('net');

console.log('🚀 Starting QR Code PIX Generator - Clean Architecture Demo\n');

/**
 * Check if a port is available
 */
function isPortAvailable(port) {
  return new Promise(resolve => {
    const server = net.createServer();
    server.listen(port, () => {
      server.once('close', () => resolve(true));
      server.close();
    });
    server.on('error', () => resolve(false));
  });
}

/**
 * Kill process using a specific port
 */
function killProcessOnPort(port) {
  return new Promise(resolve => {
    exec(`lsof -ti:${port}`, (error, stdout) => {
      if (error || !stdout.trim()) {
        resolve(false);
        return;
      }

      const pids = stdout.trim().split('\n');
      pids.forEach(pid => {
        try {
          process.kill(parseInt(pid), 'SIGTERM');
          console.log(`🔄 Killed process ${pid} using port ${port}`);
        } catch (e) {
          // Process might already be dead
        }
      });

      // Wait a bit for processes to die
      setTimeout(() => resolve(true), 1000);
    });
  });
}

async function main() {
  try {
    // Check if we're in the right directory
    const packageJsonPath = path.join(__dirname, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      console.error('❌ package.json not found. Please run this from the src-new directory.');
      process.exit(1);
    }

    // Check if port 3000 is available
    const port = 3000;
    const portAvailable = await isPortAvailable(port);

    if (!portAvailable) {
      console.log(`⚠️  Port ${port} is already in use. Attempting to free it...`);
      const killed = await killProcessOnPort(port);

      if (killed) {
        // Check again after killing
        const nowAvailable = await isPortAvailable(port);
        if (!nowAvailable) {
          console.error(
            `❌ Could not free port ${port}. Please manually stop the process using this port.`
          );
          console.log(`💡 Try running: lsof -ti:${port} | xargs kill -9`);
          process.exit(1);
        }
      } else {
        console.error(
          `❌ Could not free port ${port}. Please manually stop the process using this port.`
        );
        console.log(`💡 Try running: lsof -ti:${port} | xargs kill -9`);
        process.exit(1);
      }
    }

    console.log('📦 Installing dependencies...');
    execSync('npm install', { stdio: 'inherit', cwd: __dirname });

    console.log('\n🔨 Building TypeScript project...');
    execSync('npm run build', { stdio: 'inherit', cwd: __dirname });

    console.log('\n🌐 Starting development server...');
    console.log(`📖 Demo will be available at: http://localhost:${port}`);
    console.log('🎯 Features to test:');
    console.log('   • PIX key validation (CPF, email, phone, random)');
    console.log('   • BR-Code generation');
    console.log('   • QR code generation with customization');
    console.log('   • Performance monitoring');
    console.log('   • Clean Architecture layers integration');
    console.log('\n⚡ Press Ctrl+C to stop the server\n');

    // Start the server
    execSync('npm run serve', { stdio: 'inherit', cwd: __dirname });
  } catch (error) {
    console.error('❌ Error starting the demo:', error.message);

    console.log('\n🛠️  Manual steps to run the demo:');
    console.log('1. cd src-new');
    console.log('2. npm install');
    console.log('3. npm run build');
    console.log('4. npm run serve');
    console.log('5. Open http://localhost:3000 in your browser');

    console.log('\n🔧 If port 3000 is busy, try:');
    console.log('   lsof -ti:3000 | xargs kill -9');
    console.log('   Then run the script again');

    process.exit(1);
  }
}

main();
