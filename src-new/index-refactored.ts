/**
 * Complete refactored library entry point
 * Exports all layers following Clean Architecture
 */

// === DOMAIN LAYER ===
export { PixKey } from './domain/value-objects/pix-key';
export { Money } from './domain/value-objects/money';
export { PixTransaction } from './domain/entities/pix-transaction';
export { BRCodeGenerator } from './domain/services/br-code-generator';

// === APPLICATION LAYER ===
import { GeneratePixQRCodeUseCase } from './application/usecases/generate-pix-qrcode';
export { 
  GeneratePixQRCodeUseCase,
  type GeneratePixQRCodeRequest,
  type GeneratePixQRCodeResponse,
  type QRCodeRepository
} from './application/usecases/generate-pix-qrcode';

// === INFRASTRUCTURE LAYER ===
// QR Generators
export { QRCodeStylingProvider } from './infrastructure/qr-generators/qr-code-styling-provider';
export { ExternalAPIProvider } from './infrastructure/qr-generators/external-api-provider';
export { QRGeneratorFactory } from './infrastructure/qr-generators/qr-generator-factory';

// File System
export { FileOperationsService } from './infrastructure/file-system/file-operations';

// Repositories
import { createEnhancedQRCodeRepository } from './infrastructure/repositories/qr-code-repository-enhanced';
export { 
  QRCodeRepositoryEnhanced,
  createEnhancedQRCodeRepository,
  createQRCodeRepositoryWithStyling,
  createQRCodeRepositoryWithExternalAPI
} from './infrastructure/repositories/qr-code-repository-enhanced';

// === PRESENTATION LAYER ===
// Controllers
import { PixController } from './presentation/controllers/pix-controller';
export { PixController } from './presentation/controllers/pix-controller';

// Web Components
export { PixFormComponent } from './presentation/web/components/pix-form-component';
export { QRCustomizationComponent } from './presentation/web/components/qr-customization-component';
export { QRDisplayComponent } from './presentation/web/components/qr-display-component';

// Main App Controller
export { AppController } from './presentation/web/app-controller';

// Integration helpers
import { initializeFromHTML, checkHTMLCompatibility } from './presentation/web/integration-guide';
export { 
  initializeFromHTML,
  initializeLegacyCompatibility,
  checkHTMLCompatibility 
} from './presentation/web/integration-guide';

// === SHARED LAYER ===
export * from './shared/types/pix-types';
export * from './shared/errors/pix-errors';

// === FACTORY FUNCTIONS ===

/**
 * Complete application setup with all dependencies
 */
export function createPixQRApplication(config?: {
  preferredQRProvider?: 'qr-code-styling' | 'external-api' | 'auto';
  fallbackEnabled?: boolean;
}) {
  let repositoryConfig: Parameters<typeof createEnhancedQRCodeRepository>[0];
  
  if (config) {
    repositoryConfig = {};
    if (config.preferredQRProvider) {
      repositoryConfig.preferredProvider = config.preferredQRProvider;
    }
    if (config.fallbackEnabled !== undefined) {
      repositoryConfig.fallbackEnabled = config.fallbackEnabled;
    }
  }
  
  const repository = createEnhancedQRCodeRepository(repositoryConfig);
  const useCase = new GeneratePixQRCodeUseCase(repository);
  const controller = new PixController(useCase);
  
  return {
    repository,
    useCase,
    controller,
    
    // Convenience methods
    async generatePixQR(data: {
      keyType: 'cpf' | 'phone' | 'email' | 'random';
      pixKey: string;
      receiverName: string;
      receiverCity: string;
      amount?: number;
      reference?: string;
      description?: string;
    }) {
      return await controller.generateQRCode(data);
    },
    
    async validatePixKey(pixKey: string, keyType: 'cpf' | 'phone' | 'email' | 'random') {
      return await controller.validatePixKey(pixKey, keyType);
    }
  };
}

/**
 * Quick setup for web applications (drop-in replacement)
 */
export function initializeWebApp() {
  return initializeFromHTML();
}

/**
 * Server-side setup (Node.js compatible)
 */
export function createServerSideApp() {
  return createPixQRApplication({
    preferredQRProvider: 'external-api',
    fallbackEnabled: false
  });
}

// === VERSION INFO ===
export const version = {
  version: '2.0.0',
  architecture: 'Clean Architecture',
  typescript: true,
  features: [
    'Domain-driven design',
    'Type-safe error handling', 
    'Multiple QR providers',
    'Zero-margin QR generation',
    'Advanced customization',
    'File operations',
    'Web components',
    'Legacy compatibility'
  ]
};

// === DEVELOPMENT HELPERS ===
export const dev = {
  checkCompatibility: checkHTMLCompatibility,
  
  // Debug information
  getDependencyTree: () => ({
    domain: ['PixKey', 'Money', 'PixTransaction', 'BRCodeGenerator'],
    application: ['GeneratePixQRCodeUseCase'],
    infrastructure: ['QRGeneratorFactory', 'FileOperationsService', 'QRCodeRepositoryEnhanced'],
    presentation: ['AppController', 'PixFormComponent', 'QRCustomizationComponent', 'QRDisplayComponent'],
    shared: ['Types', 'Errors', 'Constants']
  }),
  
  // Architecture validation
  validateArchitecture: () => {
    const violations: string[] = [];
    
    // Check that domain doesn't import from other layers
    // This would be implemented with static analysis in a real project
    
    return {
      isValid: violations.length === 0,
      violations
    };
  }
};

// Default export for easy importing
export default {
  createPixQRApplication,
  initializeWebApp,
  createServerSideApp,
  version,
  dev
};