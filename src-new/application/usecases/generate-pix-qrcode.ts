/**
 * Generate PIX QR Code Use Case
 * Orchestrates the generation of PIX QR codes with customization options
 */

import { PixTransaction } from '../../domain/entities/pix-transaction';
import { BRCodeGenerator } from '../../domain/services/br-code-generator';
import { QRCustomizationOptions, QRGenerationOptions } from '../../shared/types/pix-types';
import { QRGenerationError } from '../../shared/errors/pix-errors';

export interface GeneratePixQRCodeRequest {
  transaction: PixTransaction;
  customization?: QRCustomizationOptions;
  isDynamic?: boolean;
}

export interface GeneratePixQRCodeResponse {
  brCode: string;
  qrCodeDataUrl?: string;
  transaction: PixTransaction;
}

export interface QRCodeRepository {
  generateQRCode(options: QRGenerationOptions): Promise<string>;
  generateZeroMarginQRCode(options: QRGenerationOptions): Promise<string>;
}

export class GeneratePixQRCodeUseCase {
  constructor(private qrCodeRepository: QRCodeRepository) {}

  async execute(request: GeneratePixQRCodeRequest): Promise<GeneratePixQRCodeResponse> {
    try {
      // Generate BR Code using domain service
      const brCode = BRCodeGenerator.generate(request.transaction, request.isDynamic || false);

      let qrCodeDataUrl: string | undefined;

      if (request.customization) {
        // Generate customized QR code
        const qrOptions = this.buildQROptions(brCode, request.customization);
        
        if (request.customization.imageMargin === 0) {
          qrCodeDataUrl = await this.qrCodeRepository.generateZeroMarginQRCode(qrOptions);
        } else {
          qrCodeDataUrl = await this.qrCodeRepository.generateQRCode(qrOptions);
        }
      }

      const response: GeneratePixQRCodeResponse = {
        brCode,
        transaction: request.transaction
      };

      if (qrCodeDataUrl) {
        response.qrCodeDataUrl = qrCodeDataUrl;
      }

      return response;

    } catch (error) {
      if (error instanceof QRGenerationError) {
        throw error;
      }
      
      throw new QRGenerationError(
        error instanceof Error ? error.message : 'Unknown error occurred'
      );
    }
  }

  private buildQROptions(brCode: string, customization: QRCustomizationOptions): QRGenerationOptions {
    return {
      width: customization.qrSize,
      height: customization.qrSize,
      type: 'canvas',
      data: brCode,
      margin: customization.imageMargin,
      qrOptions: {
        typeNumber: 0, // Auto-detect
        mode: undefined,
        errorCorrectionLevel: 'M' // Standard level for consistency
      },
      dotsOptions: {
        color: customization.dotsColor,
        type: customization.dotsType,
        roundSize: true
      },
      backgroundOptions: {
        color: customization.backgroundColor,
        round: 0
      },
      cornersSquareOptions: {
        color: customization.cornerSquareColor,
        type: customization.cornerSquareType
      },
      cornersDotOptions: {
        color: customization.cornerDotColor,
        type: customization.cornerDotType
      }
    };
  }
}