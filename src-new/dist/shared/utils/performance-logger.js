"use strict";
/**
 * Performance Logger
 * Centralized performance monitoring and logging
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.performanceLogger = exports.PerformanceLogger = void 0;
exports.measurePerformance = measurePerformance;
exports.usePerformanceMonitoring = usePerformanceMonitoring;
class PerformanceLogger {
    constructor(maxMetricsPerName = 1000, enableLogging = true) {
        this.metrics = new Map();
        this.maxMetricsPerName = maxMetricsPerName;
        this.enableLogging = enableLogging;
    }
    static getInstance(maxMetricsPerName = 1000, enableLogging = true) {
        if (!PerformanceLogger.instance) {
            PerformanceLogger.instance = new PerformanceLogger(maxMetricsPerName, enableLogging);
        }
        return PerformanceLogger.instance;
    }
    startMeasurement(name, metadata) {
        if (!this.enableLogging) {
            return () => { }; // No-op if logging is disabled
        }
        const startTime = performance.now();
        const timestamp = Date.now();
        return () => {
            const duration = performance.now() - startTime;
            const metric = {
                name,
                duration,
                timestamp
            };
            if (metadata) {
                metric.metadata = metadata;
            }
            this.recordMetric(metric);
        };
    }
    async measureAsync(name, asyncFunction, metadata) {
        if (!this.enableLogging) {
            return asyncFunction();
        }
        const endMeasurement = this.startMeasurement(name, metadata);
        try {
            const result = await asyncFunction();
            return result;
        }
        finally {
            endMeasurement();
        }
    }
    measure(name, syncFunction, metadata) {
        if (!this.enableLogging) {
            return syncFunction();
        }
        const endMeasurement = this.startMeasurement(name, metadata);
        try {
            return syncFunction();
        }
        finally {
            endMeasurement();
        }
    }
    recordMetric(metric) {
        if (!this.metrics.has(metric.name)) {
            this.metrics.set(metric.name, []);
        }
        const metrics = this.metrics.get(metric.name);
        metrics.push(metric);
        // Keep only the most recent metrics to prevent memory leaks
        if (metrics.length > this.maxMetricsPerName) {
            metrics.splice(0, metrics.length - this.maxMetricsPerName);
        }
        // Log slow operations
        if (metric.duration > 100) { // More than 100ms
            console.warn(`🐌 Slow operation detected: ${metric.name} took ${metric.duration.toFixed(2)}ms`);
        }
    }
    getStats(name) {
        const metrics = this.metrics.get(name);
        if (!metrics || metrics.length === 0) {
            return null;
        }
        const durations = metrics.map(m => m.duration).sort((a, b) => a - b);
        const totalDuration = durations.reduce((sum, duration) => sum + duration, 0);
        return {
            count: metrics.length,
            totalDuration,
            averageDuration: totalDuration / metrics.length,
            minDuration: durations[0] || 0,
            maxDuration: durations[durations.length - 1] || 0,
            percentile95: durations[Math.floor(durations.length * 0.95)] || durations[durations.length - 1] || 0,
            lastExecuted: Math.max(...metrics.map(m => m.timestamp))
        };
    }
    getAllStats() {
        const result = {};
        for (const name of this.metrics.keys()) {
            const stats = this.getStats(name);
            if (stats) {
                result[name] = stats;
            }
        }
        return result;
    }
    getMetrics(name) {
        return this.metrics.get(name) || null;
    }
    generateReport() {
        const stats = this.getAllStats();
        const sortedEntries = Object.entries(stats)
            .sort(([, a], [, b]) => b.averageDuration - a.averageDuration);
        let report = '📊 Performance Report\n';
        report += '='.repeat(50) + '\n\n';
        if (sortedEntries.length === 0) {
            report += 'No performance metrics recorded.\n';
            return report;
        }
        report += `${'Operation'.padEnd(25)} | ${'Count'.padStart(6)} | ${'Avg (ms)'.padStart(8)} | ${'95th (ms)'.padStart(9)} | ${'Max (ms)'.padStart(8)}\n`;
        report += '-'.repeat(65) + '\n';
        for (const [name, stat] of sortedEntries) {
            report += `${name.padEnd(25)} | ${stat.count.toString().padStart(6)} | `;
            report += `${stat.averageDuration.toFixed(2).padStart(8)} | `;
            report += `${stat.percentile95.toFixed(2).padStart(9)} | `;
            report += `${stat.maxDuration.toFixed(2).padStart(8)}\n`;
        }
        report += '\n🎯 Performance Insights:\n';
        // Find the slowest operations
        const slowOperations = sortedEntries.filter(([, stat]) => stat.averageDuration > 10);
        if (slowOperations.length > 0) {
            report += `• Slowest operations (>10ms avg):\n`;
            slowOperations.slice(0, 3).forEach(([name, stat]) => {
                report += `  - ${name}: ${stat.averageDuration.toFixed(2)}ms avg\n`;
            });
        }
        // Find operations with high variance
        const highVarianceOps = sortedEntries.filter(([, stat]) => {
            const variance = stat.maxDuration - stat.minDuration;
            return variance > stat.averageDuration * 2; // High variance
        });
        if (highVarianceOps.length > 0) {
            report += `• High variance operations:\n`;
            highVarianceOps.slice(0, 3).forEach(([name, stat]) => {
                const variance = stat.maxDuration - stat.minDuration;
                report += `  - ${name}: ${variance.toFixed(2)}ms variance (${stat.minDuration.toFixed(2)}-${stat.maxDuration.toFixed(2)}ms)\n`;
            });
        }
        // Memory usage recommendations
        const totalOperations = Object.values(stats).reduce((sum, stat) => sum + stat.count, 0);
        report += `\n📈 Total operations monitored: ${totalOperations}\n`;
        report += `📊 Unique operation types: ${Object.keys(stats).length}\n`;
        return report;
    }
    clear() {
        this.metrics.clear();
    }
    clearMetrics(name) {
        this.metrics.delete(name);
    }
    exportMetrics() {
        const result = {};
        for (const [name, metrics] of this.metrics.entries()) {
            result[name] = [...metrics]; // Create a copy
        }
        return result;
    }
    importMetrics(data) {
        for (const [name, metrics] of Object.entries(data)) {
            this.metrics.set(name, [...metrics]);
        }
    }
    // Integration with domain operations
    static measureDomainOperation(operationName, operation, entityType) {
        const logger = PerformanceLogger.getInstance();
        return logger.measure(`domain.${operationName}`, operation, entityType ? { entityType } : undefined);
    }
    static async measureApplicationOperation(useCaseName, operation, requestSize) {
        const logger = PerformanceLogger.getInstance();
        return logger.measureAsync(`application.${useCaseName}`, operation, requestSize ? { requestSize } : undefined);
    }
    static measureInfrastructureOperation(operationName, operation, provider) {
        const logger = PerformanceLogger.getInstance();
        return logger.measure(`infrastructure.${operationName}`, operation, provider ? { provider } : undefined);
    }
}
exports.PerformanceLogger = PerformanceLogger;
// Decorator for automatic performance measurement
function measurePerformance(name, includeParams = false) {
    return function (target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;
        const operationName = name || `${target.constructor.name}.${propertyKey}`;
        descriptor.value = function (...args) {
            const logger = PerformanceLogger.getInstance();
            const metadata = includeParams ? { paramCount: args.length } : undefined;
            if (originalMethod.constructor.name === 'AsyncFunction') {
                return logger.measureAsync(operationName, () => originalMethod.apply(this, args), metadata);
            }
            else {
                return logger.measure(operationName, () => originalMethod.apply(this, args), metadata);
            }
        };
        return descriptor;
    };
}
// React-like hook for performance monitoring in components
function usePerformanceMonitoring(componentName) {
    const logger = PerformanceLogger.getInstance();
    const measureRender = (renderFunction) => {
        logger.measure(`render.${componentName}`, renderFunction);
    };
    const measureInteraction = (interactionName, interaction) => {
        logger.measure(`interaction.${componentName}.${interactionName}`, interaction);
    };
    const getComponentStats = () => {
        return {
            render: logger.getStats(`render.${componentName}`),
            interactions: Object.keys(logger.getAllStats())
                .filter(key => key.startsWith(`interaction.${componentName}.`))
                .reduce((acc, key) => {
                const interactionName = key.replace(`interaction.${componentName}.`, '');
                acc[interactionName] = logger.getStats(key);
                return acc;
            }, {})
        };
    };
    return {
        measureRender,
        measureInteraction,
        getComponentStats
    };
}
// Global performance logger instance
exports.performanceLogger = PerformanceLogger.getInstance();
//# sourceMappingURL=performance-logger.js.map