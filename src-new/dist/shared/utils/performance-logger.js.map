{"version": 3, "file": "performance-logger.js", "sourceRoot": "", "sources": ["../../../shared/utils/performance-logger.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AA8QH,gDAkBC;AAGD,4DA6BC;AA7SD,MAAa,iBAAiB;IAM5B,YAAoB,iBAAiB,GAAG,IAAI,EAAE,aAAa,GAAG,IAAI;QAJ1D,YAAO,GAAqC,IAAI,GAAG,EAAE,CAAC;QAK5D,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,iBAAiB,GAAG,IAAI,EAAE,aAAa,GAAG,IAAI;QAC/D,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC;YAChC,iBAAiB,CAAC,QAAQ,GAAG,IAAI,iBAAiB,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;QACvF,CAAC;QACD,OAAO,iBAAiB,CAAC,QAAQ,CAAC;IACpC,CAAC;IAED,gBAAgB,CAAC,IAAY,EAAE,QAA8B;QAC3D,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC,+BAA+B;QAClD,CAAC;QAED,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,OAAO,GAAS,EAAE;YAChB,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC/C,MAAM,MAAM,GAAQ;gBAClB,IAAI;gBACJ,QAAQ;gBACR,SAAS;aACV,CAAC;YAEF,IAAI,QAAQ,EAAE,CAAC;gBACb,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAC7B,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,IAAY,EACZ,aAA+B,EAC/B,QAA8B;QAE9B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO,aAAa,EAAE,CAAC;QACzB,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC7D,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,aAAa,EAAE,CAAC;YACrC,OAAO,MAAM,CAAC;QAChB,CAAC;gBAAS,CAAC;YACT,cAAc,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED,OAAO,CACL,IAAY,EACZ,YAAqB,EACrB,QAA8B;QAE9B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO,YAAY,EAAE,CAAC;QACxB,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAC7D,IAAI,CAAC;YACH,OAAO,YAAY,EAAE,CAAC;QACxB,CAAC;gBAAS,CAAC;YACT,cAAc,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,MAAyB;QAC5C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACpC,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAE,CAAC;QAC/C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAErB,4DAA4D;QAC5D,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5C,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7D,CAAC;QAED,sBAAsB;QACtB,IAAI,MAAM,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC,CAAC,kBAAkB;YAC7C,OAAO,CAAC,IAAI,CAAC,+BAA+B,MAAM,CAAC,IAAI,SAAS,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAClG,CAAC;IACH,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACrE,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC;QAE7E,OAAO;YACL,KAAK,EAAE,OAAO,CAAC,MAAM;YACrB,aAAa;YACb,eAAe,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM;YAC/C,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;YAC9B,WAAW,EAAE,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC;YACjD,YAAY,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC;YACpG,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;SACzD,CAAC;IACJ,CAAC;IAED,WAAW;QACT,MAAM,MAAM,GAAqC,EAAE,CAAC;QACpD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;YACvC,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YACvB,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,UAAU,CAAC,IAAY;QACrB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IACxC,CAAC;IAED,cAAc;QACZ,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACjC,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;aACxC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,eAAe,CAAC,CAAC;QAEjE,IAAI,MAAM,GAAG,yBAAyB,CAAC;QACvC,MAAM,IAAI,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;QAEnC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,oCAAoC,CAAC;YAC/C,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QACtJ,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;QAEhC,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,aAAa,EAAE,CAAC;YACzC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;YACzE,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;YAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC;YAC3D,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3D,CAAC;QAED,MAAM,IAAI,8BAA8B,CAAC;QAEzC,8BAA8B;QAC9B,MAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC,CAAC;QACrF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,qCAAqC,CAAC;YAChD,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBAClD,MAAM,IAAI,OAAO,IAAI,KAAK,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;YACtE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,qCAAqC;QACrC,MAAM,eAAe,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE;YACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YACrD,OAAO,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,gBAAgB;QAC9D,CAAC,CAAC,CAAC;QAEH,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,+BAA+B,CAAC;YAC1C,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;gBACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBACrD,MAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;YACjI,CAAC,CAAC,CAAC;QACL,CAAC;QAED,+BAA+B;QAC/B,MAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACxF,MAAM,IAAI,oCAAoC,eAAe,IAAI,CAAC;QAClE,MAAM,IAAI,8BAA8B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC;QAEtE,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK;QACH,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAED,YAAY,CAAC,IAAY;QACvB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED,aAAa;QACX,MAAM,MAAM,GAAwC,EAAE,CAAC;QACvD,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YACrD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,gBAAgB;QAC/C,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,aAAa,CAAC,IAAyC;QACrD,KAAK,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,qCAAqC;IACrC,MAAM,CAAC,sBAAsB,CAC3B,aAAqB,EACrB,SAAkB,EAClB,UAAmB;QAEnB,MAAM,MAAM,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC;QAC/C,OAAO,MAAM,CAAC,OAAO,CACnB,UAAU,aAAa,EAAE,EACzB,SAAS,EACT,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,SAAS,CACxC,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,2BAA2B,CACtC,WAAmB,EACnB,SAA2B,EAC3B,WAAoB;QAEpB,MAAM,MAAM,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC;QAC/C,OAAO,MAAM,CAAC,YAAY,CACxB,eAAe,WAAW,EAAE,EAC5B,SAAS,EACT,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS,CAC1C,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,8BAA8B,CACnC,aAAqB,EACrB,SAAkB,EAClB,QAAiB;QAEjB,MAAM,MAAM,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC;QAC/C,OAAO,MAAM,CAAC,OAAO,CACnB,kBAAkB,aAAa,EAAE,EACjC,SAAS,EACT,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,SAAS,CACpC,CAAC;IACJ,CAAC;CACF;AAxPD,8CAwPC;AAED,kDAAkD;AAClD,SAAgB,kBAAkB,CAAC,IAAa,EAAE,aAAa,GAAG,KAAK;IACrE,OAAO,UAAU,MAAW,EAAE,WAAmB,EAAE,UAA8B;QAC/E,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QACxC,MAAM,aAAa,GAAG,IAAI,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,IAAI,WAAW,EAAE,CAAC;QAE1E,UAAU,CAAC,KAAK,GAAG,UAAU,GAAG,IAAW;YACzC,MAAM,MAAM,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC;YAC/C,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;YAEzE,IAAI,cAAc,CAAC,WAAW,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBACxD,OAAO,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;YAC9F,CAAC;iBAAM,CAAC;gBACN,OAAO,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;YACzF,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;AACJ,CAAC;AAED,2DAA2D;AAC3D,SAAgB,wBAAwB,CAAC,aAAqB;IAC5D,MAAM,MAAM,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC;IAE/C,MAAM,aAAa,GAAG,CAAC,cAA0B,EAAE,EAAE;QACnD,MAAM,CAAC,OAAO,CAAC,UAAU,aAAa,EAAE,EAAE,cAAc,CAAC,CAAC;IAC5D,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,CAAC,eAAuB,EAAE,WAAuB,EAAE,EAAE;QAC9E,MAAM,CAAC,OAAO,CAAC,eAAe,aAAa,IAAI,eAAe,EAAE,EAAE,WAAW,CAAC,CAAC;IACjF,CAAC,CAAC;IAEF,MAAM,iBAAiB,GAAG,GAAG,EAAE;QAC7B,OAAO;YACL,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,UAAU,aAAa,EAAE,CAAC;YAClD,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;iBAC5C,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,eAAe,aAAa,GAAG,CAAC,CAAC;iBAC9D,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACnB,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,aAAa,GAAG,EAAE,EAAE,CAAC,CAAC;gBACzE,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBAC5C,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAA6C,CAAC;SACpD,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;QACL,aAAa;QACb,kBAAkB;QAClB,iBAAiB;KAClB,CAAC;AACJ,CAAC;AAED,qCAAqC;AACxB,QAAA,iBAAiB,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC"}