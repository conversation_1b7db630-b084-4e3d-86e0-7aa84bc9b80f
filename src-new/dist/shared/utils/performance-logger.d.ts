/**
 * Performance Logger
 * Centralized performance monitoring and logging
 */
export interface PerformanceMetric {
    name: string;
    duration: number;
    timestamp: number;
    metadata?: Record<string, any>;
}
export interface PerformanceStats {
    count: number;
    totalDuration: number;
    averageDuration: number;
    minDuration: number;
    maxDuration: number;
    percentile95: number;
    lastExecuted: number;
}
export declare class PerformanceLogger {
    private static instance;
    private metrics;
    private readonly maxMetricsPerName;
    private readonly enableLogging;
    private constructor();
    static getInstance(maxMetricsPerName?: number, enableLogging?: boolean): PerformanceLogger;
    startMeasurement(name: string, metadata?: Record<string, any>): () => void;
    measureAsync<T>(name: string, asyncFunction: () => Promise<T>, metadata?: Record<string, any>): Promise<T>;
    measure<T>(name: string, syncFunction: () => T, metadata?: Record<string, any>): T;
    private recordMetric;
    getStats(name: string): PerformanceStats | null;
    getAllStats(): Record<string, PerformanceStats>;
    getMetrics(name: string): PerformanceMetric[] | null;
    generateReport(): string;
    clear(): void;
    clearMetrics(name: string): void;
    exportMetrics(): Record<string, PerformanceMetric[]>;
    importMetrics(data: Record<string, PerformanceMetric[]>): void;
    static measureDomainOperation<T>(operationName: string, operation: () => T, entityType?: string): T;
    static measureApplicationOperation<T>(useCaseName: string, operation: () => Promise<T>, requestSize?: number): Promise<T>;
    static measureInfrastructureOperation<T>(operationName: string, operation: () => T, provider?: string): T;
}
export declare function measurePerformance(name?: string, includeParams?: boolean): (target: any, propertyKey: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function usePerformanceMonitoring(componentName: string): {
    measureRender: (renderFunction: () => void) => void;
    measureInteraction: (interactionName: string, interaction: () => void) => void;
    getComponentStats: () => {
        render: PerformanceStats | null;
        interactions: Record<string, PerformanceStats | null>;
    };
};
export declare const performanceLogger: PerformanceLogger;
//# sourceMappingURL=performance-logger.d.ts.map