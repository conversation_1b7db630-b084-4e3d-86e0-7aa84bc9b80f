/**
 * Types for PIX QR Code Generation
 */
export type PixKeyType = 'cpf' | 'phone' | 'email' | 'random';
export interface PixFormData {
    keyType: PixKeyType;
    pixKey: string;
    receiverName: string;
    receiverCity: string;
    amount: number;
    reference?: string;
    description?: string;
}
export interface QRCustomizationOptions {
    dotsType: string;
    dotsColor: string;
    cornerSquareType: string;
    cornerSquareColor: string;
    cornerDotType: string;
    cornerDotColor: string;
    backgroundColor: string;
    qrSize: number;
    imageSize: number;
    imageMargin: number;
    hideBackgroundDots: boolean;
}
export interface QRGenerationOptions {
    width: number;
    height: number;
    type: 'svg' | 'canvas';
    data: string;
    margin: number;
    qrOptions: {
        typeNumber: number;
        mode?: undefined;
        errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H';
    };
    dotsOptions: {
        color: string;
        type: string;
        roundSize: boolean;
    };
    backgroundOptions: {
        color: string;
        round: number;
    };
    cornersSquareOptions: {
        color: string;
        type?: string;
    };
    cornersDotOptions: {
        color: string;
        type?: string;
    };
    imageOptions?: {
        crossOrigin: string;
        margin: number;
        imageSize: number;
        hideBackgroundDots: boolean;
        saveAsBlob?: boolean;
    };
}
export interface ValidationResult {
    isValid: boolean;
    message: string;
}
export interface ToastOptions {
    message: string;
    type: 'success' | 'error' | 'warning';
}
//# sourceMappingURL=pix-types.d.ts.map