/**
 * Custom error classes for PIX operations
 */
export declare class PixError extends <PERSON>rror {
    constructor(message: string);
}
export declare class ValidationError extends PixError {
    constructor(field: string, message: string);
}
export declare class QRGenerationError extends PixError {
    constructor(message: string, originalError?: Error);
}
export declare class BRCodeGenerationError extends PixError {
    constructor(message: string);
}
//# sourceMappingURL=pix-errors.d.ts.map