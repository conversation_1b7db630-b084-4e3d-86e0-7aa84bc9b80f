"use strict";
/**
 * Custom error classes for PIX operations
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BRCodeGenerationError = exports.QRGenerationError = exports.ValidationError = exports.PixError = void 0;
class PixError extends Error {
    constructor(message) {
        super(message);
        this.name = 'PixError';
    }
}
exports.PixError = PixError;
class ValidationError extends PixError {
    constructor(field, message) {
        super(`Validation error in field "${field}": ${message}`);
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
class QRGenerationError extends PixError {
    constructor(message, originalError) {
        super(`QR Code generation failed: ${message}`);
        this.name = 'QRGenerationError';
        if (originalError && originalError.stack) {
            this.stack = originalError.stack;
        }
    }
}
exports.QRGenerationError = QRGenerationError;
class BRCodeGenerationError extends PixError {
    constructor(message) {
        super(`BR Code generation failed: ${message}`);
        this.name = 'BRCodeGenerationError';
    }
}
exports.BRCodeGenerationError = BRCodeGenerationError;
//# sourceMappingURL=pix-errors.js.map