!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.QRCodeStyling=t():e.QRCodeStyling=t()}(this,()=>(()=>{"use strict";var e={3:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BRCodeGenerator=void 0;const n=r(514),o=r(934);class a{static generate(e,t=!1){return o.PerformanceLogger.measureDomainOperation("brcode.generate",()=>{try{const r=[this.createField("00",a.PAYLOAD_FORMAT_INDICATOR),this.createField("01",t?a.POINT_OF_INITIATION_DYNAMIC:a.POINT_OF_INITIATION_STATIC),this.createAccountInformation(e),this.createField("52",a.MERCHANT_CATEGORY_CODE),this.createField("53",a.CURRENCY_CODE)];e.isFreeAmount()||r.push(this.createField("54",e.amount.toDecimalString())),r.push(this.createField("58",a.COUNTRY_CODE),this.createField("59",this.formatText(e.receiverName)),this.createField("60",this.formatText(e.receiverCity)),this.createAdditionalDataField(e),"6304");const n=r.join("");return n+this.calculateCRC16(n)}catch(e){throw new n.BRCodeGenerationError(e instanceof Error?e.message:"Unknown error occurred")}},"PixTransaction")}static createAccountInformation(e){const t=this.createField("00",a.PIX_URL);let r=this.createField("01",e.pixKey.value);return e.description&&(r+=this.createField("02",this.formatText(e.description))),this.createField("26",t+r)}static createAdditionalDataField(e){const t=e.reference?this.formatText(e.reference):"***";return this.createField("62",this.createField("05",t))}static createField(e,t){return`${e}${t.length.toString().padStart(2,"0")}${t}`}static formatText(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"").replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g,"").trim()}static calculateCRC16(e){let t=65535;const r=(new TextEncoder).encode(e);for(const e of r){t^=e<<8;for(let e=0;e<8;e++)32768&t?t=t<<1^4129:t<<=1}return(65535&t).toString(16).toUpperCase().padStart(4,"0")}}t.BRCodeGenerator=a,a.PAYLOAD_FORMAT_INDICATOR="01",a.POINT_OF_INITIATION_STATIC="11",a.POINT_OF_INITIATION_DYNAMIC="12",a.PIX_URL="br.gov.bcb.pix",a.MERCHANT_CATEGORY_CODE="0000",a.CURRENCY_CODE="986",a.COUNTRY_CODE="BR"},14:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},87:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Money=void 0;const n=r(514);class o{constructor(e){this._amount=e}static create(e){if(e<0)throw new n.ValidationError("amount","Amount cannot be negative");if(e.toFixed(2).length>o.MAX_DIGITS)throw new n.ValidationError("amount",`Amount exceeds maximum of ${o.MAX_DIGITS} characters`);return new o(e)}static zero(){return new o(0)}get amount(){return this._amount}getValue(){return this._amount}toBrazilianCurrency(){return`R$ ${this._amount.toFixed(2).replace(".",",")}`}toDecimalString(){return this._amount.toFixed(2)}isZero(){return 0===this._amount}equals(e){return Math.abs(this._amount-e._amount)<.001}add(e){return o.create(this._amount+e._amount)}subtract(e){return o.create(this._amount-e._amount)}}t.Money=o,o.MAX_DIGITS=13},118:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PixTransaction=void 0;const n=r(514);class o{constructor(e,t,r,n,o,a){this._pixKey=e,this._receiverName=t,this._receiverCity=r,this._amount=n,this._reference=o,this._description=a}static create(e){const{pixKey:t,receiverName:r,receiverCity:a,amount:i,reference:s,description:c}=e;if(!r.trim())throw new n.ValidationError("receiverName","Receiver name is required");if(r.length>o.MAX_RECEIVER_NAME_LENGTH)throw new n.ValidationError("receiverName",`Receiver name cannot exceed ${o.MAX_RECEIVER_NAME_LENGTH} characters`);if(!a.trim())throw new n.ValidationError("receiverCity","Receiver city is required");if(a.length>o.MAX_RECEIVER_CITY_LENGTH)throw new n.ValidationError("receiverCity",`Receiver city cannot exceed ${o.MAX_RECEIVER_CITY_LENGTH} characters`);return new o(t,r.trim(),a.trim(),i,s?.trim(),c?.trim())}get pixKey(){return this._pixKey}get receiverName(){return this._receiverName}get receiverCity(){return this._receiverCity}get amount(){return this._amount}get reference(){return this._reference}get description(){return this._description}isFreeAmount(){return this._amount.isZero()}getDisplayDetails(){const e=[{label:"Recebedor",value:this._receiverName},{label:"Cidade",value:this._receiverCity},{label:"Chave PIX",value:this._pixKey.getFormattedValue()},{label:"Valor",value:this.isFreeAmount()?"Valor livre":this._amount.toBrazilianCurrency()}];return this._reference&&e.push({label:"Referência",value:this._reference}),this._description&&e.push({label:"Descrição",value:this._description}),e}}t.PixTransaction=o,o.MAX_RECEIVER_NAME_LENGTH=25,o.MAX_RECEIVER_CITY_LENGTH=15},368:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PixController=void 0;const n=r(432),o=r(87),a=r(118),i=r(514);t.PixController=class{constructor(e){this.generatePixQRCodeUseCase=e}async generateQRCode(e){try{const t=n.PixKey.create(e.pixKey,e.keyType),r=o.Money.create(e.amount||0),i={pixKey:t,receiverName:e.receiverName,receiverCity:e.receiverCity,amount:r};e.reference&&(i.reference=e.reference),e.description&&(i.description=e.description);const s={transaction:a.PixTransaction.create(i),isDynamic:!1};e.customization&&(s.customization=e.customization);const c=await this.generatePixQRCodeUseCase.execute(s),u={brCode:c.brCode,transactionDetails:c.transaction.getDisplayDetails()};return c.qrCodeDataUrl&&(u.qrCodeDataUrl=c.qrCodeDataUrl),{success:!0,data:u}}catch(e){return this.handleError(e)}}async validatePixKey(e,t){try{return n.PixKey.create(e,t),{isValid:!0,message:this.getSuccessMessage(t)}}catch(e){return e instanceof i.ValidationError?{isValid:!1,message:this.getErrorMessage(t)}:{isValid:!1,message:"Erro interno de validação"}}}handleError(e){if(e instanceof i.ValidationError){const t={message:e.message,code:"VALIDATION_ERROR"},r=this.extractFieldFromError(e.message);return r&&(t.field=r),{success:!1,error:t}}return e instanceof i.PixError?{success:!1,error:{message:e.message,code:e.name}}:{success:!1,error:{message:"Erro interno do servidor",code:"INTERNAL_ERROR"}}}extractFieldFromError(e){const t=e.match(/field "([^"]+)"/);return t?t[1]:void 0}getSuccessMessage(e){return{cpf:"CPF válido",phone:"Telefone válido",email:"Email válido",random:"Chave válida"}[e]}getErrorMessage(e){return{cpf:"CPF inválido",phone:"Telefone inválido",email:"Email inválido",random:"Chave deve ter pelo menos 10 caracteres"}[e]}}},432:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PixKey=void 0;const n=r(514);class o{constructor(e,t){this._value=e,this._type=t}static create(e,t){const r=this.normalize(e,t);if(!this.validate(r,t))throw new n.ValidationError("pixKey",`Invalid ${t} format`);return new o(r,t)}static normalize(e,t){switch(t){case"cpf":return e.replace(/\D/g,"");case"phone":const r=e.replace(/\D/g,"");return r.startsWith("55")?r.startsWith("+")?r:`+${r}`:`+55${r}`;case"email":case"random":return e.trim();default:throw new n.ValidationError("pixKey",`Unknown PIX key type: ${t}`)}}static validate(e,t){switch(t){case"cpf":return this.validateCPF(e);case"phone":return this.validatePhone(e);case"email":return this.validateEmail(e);case"random":return e.length>=10;default:return!1}}static validateCPF(e){if(11!==e.length)return!1;if(/^(\d)\1{10}$/.test(e))return!1;let t=0;for(let r=0;r<9;r++)t+=parseInt(e.charAt(r))*(10-r);if(10*t%11%10!==parseInt(e.charAt(9)))return!1;t=0;for(let r=0;r<10;r++)t+=parseInt(e.charAt(r))*(11-r);return 10*t%11%10===parseInt(e.charAt(10))}static validatePhone(e){const t=e.replace(/\D/g,"");return 13===t.length&&t.startsWith("55")}static validateEmail(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}get value(){return this._value}get type(){return this._type}getFormattedValue(){switch(this._type){case"cpf":return this._value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/,"$1.$2.$3-$4");case"phone":const e=this._value.replace(/\D/g,"");return 13===e.length?`+55 (${e.substring(2,4)}) ${e.substring(4,9)}-${e.substring(9)}`:this._value;default:return this._value}}equals(e){return this._value===e._value&&this._type===e._type}}t.PixKey=o},514:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BRCodeGenerationError=t.QRGenerationError=t.ValidationError=t.PixError=void 0;class r extends Error{constructor(e){super(e),this.name="PixError"}}t.PixError=r,t.ValidationError=class extends r{constructor(e,t){super(`Validation error in field "${e}": ${t}`),this.name="ValidationError"}},t.QRGenerationError=class extends r{constructor(e,t){super(`QR Code generation failed: ${e}`),this.name="QRGenerationError",t&&t.stack&&(this.stack=t.stack)}},t.BRCodeGenerationError=class extends r{constructor(e){super(`BR Code generation failed: ${e}`),this.name="BRCodeGenerationError"}}},615:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.QRCodeRepositoryImpl=void 0,t.createQRCodeRepository=function(){return new o};const n=r(514);class o{async generateQRCode(e){try{return"undefined"!=typeof window&&window.QRCodeStyling?this.generateWithQRCodeStyling(e):this.generateWithExternalAPI(e)}catch(e){throw new n.QRGenerationError(`Failed to generate QR code: ${e instanceof Error?e.message:"Unknown error"}`)}}async generateZeroMarginQRCode(e){try{const t={...e,margin:0};return this.generateWithAdvancedProcessing(t)}catch(e){throw new n.QRGenerationError(`Failed to generate zero margin QR code: ${e instanceof Error?e.message:"Unknown error"}`)}}async generateWithQRCodeStyling(e){return new Promise((t,r)=>{try{const n=new window.QRCodeStyling(e),o=document.createElement("div");o.style.position="absolute",o.style.left="-9999px",document.body.appendChild(o),n.append(o),setTimeout(()=>{const e=o.querySelector("canvas");if(e){const r=e.toDataURL();document.body.removeChild(o),t(r)}else document.body.removeChild(o),r(new Error("Failed to generate canvas"))},100)}catch(e){r(e)}})}async generateWithExternalAPI(e){const t=new URLSearchParams({size:`${e.width}x${e.height}`,data:e.data,ecc:e.qrOptions.errorCorrectionLevel,format:"png"}),r=await fetch(`https://api.qrserver.com/v1/create-qr-code/?${t.toString()}`);if(!r.ok)throw new Error(`External API request failed: ${r.statusText}`);const n=await r.blob();return new Promise(e=>{const t=new FileReader;t.onloadend=()=>e(t.result),t.readAsDataURL(n)})}async generateWithAdvancedProcessing(e){return this.generateWithQRCodeStyling(e)}}t.QRCodeRepositoryImpl=o},791:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.PixController=t.createQRCodeRepository=t.QRCodeRepositoryImpl=t.GeneratePixQRCodeUseCase=t.BRCodeGenerator=t.PixTransaction=t.Money=t.PixKey=void 0,t.createPixQRCodeService=function(){const e=(0,l.createQRCodeRepository)(),t=new d.GeneratePixQRCodeUseCase(e);return{controller:new m.PixController(t),useCase:t,repository:e}};var a=r(432);Object.defineProperty(t,"PixKey",{enumerable:!0,get:function(){return a.PixKey}});var i=r(87);Object.defineProperty(t,"Money",{enumerable:!0,get:function(){return i.Money}});var s=r(118);Object.defineProperty(t,"PixTransaction",{enumerable:!0,get:function(){return s.PixTransaction}});var c=r(3);Object.defineProperty(t,"BRCodeGenerator",{enumerable:!0,get:function(){return c.BRCodeGenerator}});var u=r(975);Object.defineProperty(t,"GeneratePixQRCodeUseCase",{enumerable:!0,get:function(){return u.GeneratePixQRCodeUseCase}});const l=r(615),d=r(975),m=r(368);var h=r(615);Object.defineProperty(t,"QRCodeRepositoryImpl",{enumerable:!0,get:function(){return h.QRCodeRepositoryImpl}}),Object.defineProperty(t,"createQRCodeRepository",{enumerable:!0,get:function(){return h.createQRCodeRepository}});var p=r(368);Object.defineProperty(t,"PixController",{enumerable:!0,get:function(){return p.PixController}}),o(r(14),t),o(r(514),t)},934:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.performanceLogger=t.PerformanceLogger=void 0,t.measurePerformance=function(e,t=!1){return function(n,o,a){const i=a.value,s=e||`${n.constructor.name}.${o}`;return a.value=function(...e){const n=r.getInstance(),o=t?{paramCount:e.length}:void 0;return"AsyncFunction"===i.constructor.name?n.measureAsync(s,()=>i.apply(this,e),o):n.measure(s,()=>i.apply(this,e),o)},a}},t.usePerformanceMonitoring=function(e){const t=r.getInstance();return{measureRender:r=>{t.measure(`render.${e}`,r)},measureInteraction:(r,n)=>{t.measure(`interaction.${e}.${r}`,n)},getComponentStats:()=>({render:t.getStats(`render.${e}`),interactions:Object.keys(t.getAllStats()).filter(t=>t.startsWith(`interaction.${e}.`)).reduce((r,n)=>(r[n.replace(`interaction.${e}.`,"")]=t.getStats(n),r),{})})}};class r{constructor(e=1e3,t=!0){this.metrics=new Map,this.maxMetricsPerName=e,this.enableLogging=t}static getInstance(e=1e3,t=!0){return r.instance||(r.instance=new r(e,t)),r.instance}startMeasurement(e,t){if(!this.enableLogging)return()=>{};const r=performance.now(),n=Date.now();return()=>{const o=performance.now()-r,a={name:e,duration:o,timestamp:n};t&&(a.metadata=t),this.recordMetric(a)}}async measureAsync(e,t,r){if(!this.enableLogging)return t();const n=this.startMeasurement(e,r);try{return await t()}finally{n()}}measure(e,t,r){if(!this.enableLogging)return t();const n=this.startMeasurement(e,r);try{return t()}finally{n()}}recordMetric(e){this.metrics.has(e.name)||this.metrics.set(e.name,[]);const t=this.metrics.get(e.name);t.push(e),t.length>this.maxMetricsPerName&&t.splice(0,t.length-this.maxMetricsPerName),e.duration>100&&console.warn(`🐌 Slow operation detected: ${e.name} took ${e.duration.toFixed(2)}ms`)}getStats(e){const t=this.metrics.get(e);if(!t||0===t.length)return null;const r=t.map(e=>e.duration).sort((e,t)=>e-t),n=r.reduce((e,t)=>e+t,0);return{count:t.length,totalDuration:n,averageDuration:n/t.length,minDuration:r[0]||0,maxDuration:r[r.length-1]||0,percentile95:r[Math.floor(.95*r.length)]||r[r.length-1]||0,lastExecuted:Math.max(...t.map(e=>e.timestamp))}}getAllStats(){const e={};for(const t of this.metrics.keys()){const r=this.getStats(t);r&&(e[t]=r)}return e}getMetrics(e){return this.metrics.get(e)||null}generateReport(){const e=this.getAllStats(),t=Object.entries(e).sort(([,e],[,t])=>t.averageDuration-e.averageDuration);let r="📊 Performance Report\n";if(r+="=".repeat(50)+"\n\n",0===t.length)return r+="No performance metrics recorded.\n",r;r+=`${"Operation".padEnd(25)} | ${"Count".padStart(6)} | ${"Avg (ms)".padStart(8)} | ${"95th (ms)".padStart(9)} | ${"Max (ms)".padStart(8)}\n`,r+="-".repeat(65)+"\n";for(const[e,n]of t)r+=`${e.padEnd(25)} | ${n.count.toString().padStart(6)} | `,r+=`${n.averageDuration.toFixed(2).padStart(8)} | `,r+=`${n.percentile95.toFixed(2).padStart(9)} | `,r+=`${n.maxDuration.toFixed(2).padStart(8)}\n`;r+="\n🎯 Performance Insights:\n";const n=t.filter(([,e])=>e.averageDuration>10);n.length>0&&(r+="• Slowest operations (>10ms avg):\n",n.slice(0,3).forEach(([e,t])=>{r+=`  - ${e}: ${t.averageDuration.toFixed(2)}ms avg\n`}));const o=t.filter(([,e])=>e.maxDuration-e.minDuration>2*e.averageDuration);o.length>0&&(r+="• High variance operations:\n",o.slice(0,3).forEach(([e,t])=>{const n=t.maxDuration-t.minDuration;r+=`  - ${e}: ${n.toFixed(2)}ms variance (${t.minDuration.toFixed(2)}-${t.maxDuration.toFixed(2)}ms)\n`}));const a=Object.values(e).reduce((e,t)=>e+t.count,0);return r+=`\n📈 Total operations monitored: ${a}\n`,r+=`📊 Unique operation types: ${Object.keys(e).length}\n`,r}clear(){this.metrics.clear()}clearMetrics(e){this.metrics.delete(e)}exportMetrics(){const e={};for(const[t,r]of this.metrics.entries())e[t]=[...r];return e}importMetrics(e){for(const[t,r]of Object.entries(e))this.metrics.set(t,[...r])}static measureDomainOperation(e,t,n){return r.getInstance().measure(`domain.${e}`,t,n?{entityType:n}:void 0)}static async measureApplicationOperation(e,t,n){return r.getInstance().measureAsync(`application.${e}`,t,n?{requestSize:n}:void 0)}static measureInfrastructureOperation(e,t,n){return r.getInstance().measure(`infrastructure.${e}`,t,n?{provider:n}:void 0)}}t.PerformanceLogger=r,t.performanceLogger=r.getInstance()},975:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GeneratePixQRCodeUseCase=void 0;const n=r(3),o=r(514);t.GeneratePixQRCodeUseCase=class{constructor(e){this.qrCodeRepository=e}async execute(e){try{const t=n.BRCodeGenerator.generate(e.transaction,e.isDynamic||!1);let r;if(e.customization){const n=this.buildQROptions(t,e.customization);r=0===e.customization.imageMargin?await this.qrCodeRepository.generateZeroMarginQRCode(n):await this.qrCodeRepository.generateQRCode(n)}const o={brCode:t,transaction:e.transaction};return r&&(o.qrCodeDataUrl=r),o}catch(e){if(e instanceof o.QRGenerationError)throw e;throw new o.QRGenerationError(e instanceof Error?e.message:"Unknown error occurred")}}buildQROptions(e,t){return{width:t.qrSize,height:t.qrSize,type:"canvas",data:e,margin:t.imageMargin,qrOptions:{typeNumber:0,mode:void 0,errorCorrectionLevel:"M"},dotsOptions:{color:t.dotsColor,type:t.dotsType,roundSize:!0},backgroundOptions:{color:t.backgroundColor,round:0},cornersSquareOptions:{color:t.cornerSquareColor,type:t.cornerSquareType},cornersDotOptions:{color:t.cornerDotColor,type:t.cornerDotType}}}}}},t={},r=function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n].call(a.exports,a,a.exports,r),a.exports}(791);return r.default})());
//# sourceMappingURL=qr-code-styling.js.map