{"version": 3, "file": "br-code-generator.js", "sourceRoot": "", "sources": ["../../../domain/services/br-code-generator.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,+DAAuE;AACvE,8EAA0E;AAE1E,MAAa,eAAe;IAS1B,MAAM,CAAC,QAAQ,CAAC,WAA2B,EAAE,YAAqB,KAAK;QACrE,OAAO,sCAAiB,CAAC,sBAAsB,CAAC,iBAAiB,EAAE,GAAG,EAAE;YACtE,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG;oBACb,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,CAAC,wBAAwB,CAAC;oBAChE,IAAI,CAAC,WAAW,CACd,IAAI,EACJ,SAAS;wBACP,CAAC,CAAC,eAAe,CAAC,2BAA2B;wBAC7C,CAAC,CAAC,eAAe,CAAC,0BAA0B,CAC/C;oBACD,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC;oBAC1C,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,CAAC,sBAAsB,CAAC;oBAC9D,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,CAAC,aAAa,CAAC;iBACtD,CAAC;gBAEF,8BAA8B;gBAC9B,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,EAAE,CAAC;oBAChC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;gBAC5E,CAAC;gBAED,MAAM,CAAC,IAAI,CACT,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,CAAC,YAAY,CAAC,EACpD,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,EACjE,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,EACjE,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,EAC3C,MAAM,CAAC,kBAAkB;iBAC1B,CAAC;gBAEF,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACzC,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAC;gBAElD,OAAO,gBAAgB,GAAG,GAAG,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,kCAAqB,CAC7B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAClE,CAAC;YACJ,CAAC;QACH,CAAC,EAAE,gBAAgB,CAAC,CAAC;IACvB,CAAC;IAEO,MAAM,CAAC,wBAAwB,CAAC,WAA2B;QACjE,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC;QAChE,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAElE,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;YAC5B,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC;QACjF,CAAC;QAED,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,GAAG,UAAU,CAAC,CAAC;IACtD,CAAC;IAEO,MAAM,CAAC,yBAAyB,CAAC,WAA2B;QAClE,MAAM,IAAI,GAAG,WAAW,CAAC,SAAS;YAChC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC;YACxC,CAAC,CAAC,KAAK,CAAC;QAEV,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IAC9D,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,GAAW,EAAE,KAAa;QACnD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACxD,OAAO,GAAG,GAAG,GAAG,MAAM,GAAG,KAAK,EAAE,CAAC;IACnC,CAAC;IAEO,MAAM,CAAC,UAAU,CAAC,IAAY;QACpC,OAAO,IAAI;aACR,SAAS,CAAC,KAAK,CAAC;aAChB,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC,oBAAoB;aACpD,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC,+BAA+B;aACxE,IAAI,EAAE,CAAC;IACZ,CAAC;IAEO,MAAM,CAAC,cAAc,CAAC,IAAY;QACxC,IAAI,GAAG,GAAG,MAAM,CAAC;QACjB,MAAM,SAAS,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEjD,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;YAC7B,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3B,IAAI,GAAG,GAAG,MAAM,EAAE,CAAC;oBACjB,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,GAAG,KAAK,CAAC,CAAC;gBACZ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IACpE,CAAC;;AAlGH,0CAmGC;AAlGyB,wCAAwB,GAAG,IAAI,CAAC;AAChC,0CAA0B,GAAG,IAAI,CAAC;AAClC,2CAA2B,GAAG,IAAI,CAAC;AACnC,uBAAO,GAAG,gBAAgB,CAAC;AAC3B,sCAAsB,GAAG,MAAM,CAAC;AAChC,6BAAa,GAAG,KAAK,CAAC,CAAC,MAAM;AAC7B,4BAAY,GAAG,IAAI,CAAC"}