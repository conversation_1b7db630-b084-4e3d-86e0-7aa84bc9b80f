/**
 * BR Code Generation Domain Service
 * Handles the business logic of generating PIX BR Codes
 */
import { PixTransaction } from '../entities/pix-transaction';
export declare class BRCodeGenerator {
    private static readonly PAYLOAD_FORMAT_INDICATOR;
    private static readonly POINT_OF_INITIATION_STATIC;
    private static readonly POINT_OF_INITIATION_DYNAMIC;
    private static readonly PIX_URL;
    private static readonly MERCHANT_CATEGORY_CODE;
    private static readonly CURRENCY_CODE;
    private static readonly COUNTRY_CODE;
    static generate(transaction: PixTransaction, isDynamic?: boolean): string;
    private static createAccountInformation;
    private static createAdditionalDataField;
    private static createField;
    private static formatText;
    private static calculateCRC16;
}
//# sourceMappingURL=br-code-generator.d.ts.map