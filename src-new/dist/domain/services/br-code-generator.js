"use strict";
/**
 * BR Code Generation Domain Service
 * Handles the business logic of generating PIX BR Codes
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BRCodeGenerator = void 0;
const pix_errors_1 = require("../../shared/errors/pix-errors");
const performance_logger_1 = require("../../shared/utils/performance-logger");
class BRCodeGenerator {
    static generate(transaction, isDynamic = false) {
        return performance_logger_1.PerformanceLogger.measureDomainOperation('brcode.generate', () => {
            try {
                const fields = [
                    this.createField('00', BRCodeGenerator.PAYLOAD_FORMAT_INDICATOR),
                    this.createField('01', isDynamic
                        ? BRCodeGenerator.POINT_OF_INITIATION_DYNAMIC
                        : BRCodeGenerator.POINT_OF_INITIATION_STATIC),
                    this.createAccountInformation(transaction),
                    this.createField('52', BRCodeGenerator.MERCHANT_CATEGORY_CODE),
                    this.createField('53', BRCodeGenerator.CURRENCY_CODE)
                ];
                // Add amount only if not zero
                if (!transaction.isFreeAmount()) {
                    fields.push(this.createField('54', transaction.amount.toDecimalString()));
                }
                fields.push(this.createField('58', BRCodeGenerator.COUNTRY_CODE), this.createField('59', this.formatText(transaction.receiverName)), this.createField('60', this.formatText(transaction.receiverCity)), this.createAdditionalDataField(transaction), '6304' // CRC placeholder
                );
                const brCodeWithoutCRC = fields.join('');
                const crc = this.calculateCRC16(brCodeWithoutCRC);
                return brCodeWithoutCRC + crc;
            }
            catch (error) {
                throw new pix_errors_1.BRCodeGenerationError(error instanceof Error ? error.message : 'Unknown error occurred');
            }
        }, 'PixTransaction');
    }
    static createAccountInformation(transaction) {
        const basePix = this.createField('00', BRCodeGenerator.PIX_URL);
        let infoString = this.createField('01', transaction.pixKey.value);
        if (transaction.description) {
            infoString += this.createField('02', this.formatText(transaction.description));
        }
        return this.createField('26', basePix + infoString);
    }
    static createAdditionalDataField(transaction) {
        const txid = transaction.reference
            ? this.formatText(transaction.reference)
            : '***';
        return this.createField('62', this.createField('05', txid));
    }
    static createField(tag, value) {
        const length = value.length.toString().padStart(2, '0');
        return `${tag}${length}${value}`;
    }
    static formatText(text) {
        return text
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
            .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '') // Keep only allowed characters
            .trim();
    }
    static calculateCRC16(data) {
        let crc = 0xFFFF;
        const dataBytes = new TextEncoder().encode(data);
        for (const byte of dataBytes) {
            crc ^= byte << 8;
            for (let i = 0; i < 8; i++) {
                if (crc & 0x8000) {
                    crc = (crc << 1) ^ 0x1021;
                }
                else {
                    crc <<= 1;
                }
            }
        }
        return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, '0');
    }
}
exports.BRCodeGenerator = BRCodeGenerator;
BRCodeGenerator.PAYLOAD_FORMAT_INDICATOR = '01';
BRCodeGenerator.POINT_OF_INITIATION_STATIC = '11';
BRCodeGenerator.POINT_OF_INITIATION_DYNAMIC = '12';
BRCodeGenerator.PIX_URL = 'br.gov.bcb.pix';
BRCodeGenerator.MERCHANT_CATEGORY_CODE = '0000';
BRCodeGenerator.CURRENCY_CODE = '986'; // BRL
BRCodeGenerator.COUNTRY_CODE = 'BR';
//# sourceMappingURL=br-code-generator.js.map