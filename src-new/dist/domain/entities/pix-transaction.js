"use strict";
/**
 * PIX Transaction Entity
 * Core business entity representing a PIX transaction
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PixTransaction = void 0;
const pix_errors_1 = require("../../shared/errors/pix-errors");
class PixTransaction {
    constructor(_pixKey, _receiverName, _receiverCity, _amount, _reference, _description) {
        this._pixKey = _pixKey;
        this._receiverName = _receiverName;
        this._receiverCity = _receiverCity;
        this._amount = _amount;
        this._reference = _reference;
        this._description = _description;
    }
    static create(params) {
        const { pixKey, receiverName, receiverCity, amount, reference, description } = params;
        // Validate receiver name
        if (!receiverName.trim()) {
            throw new pix_errors_1.ValidationError('receiverName', 'Receiver name is required');
        }
        if (receiverName.length > PixTransaction.MAX_RECEIVER_NAME_LENGTH) {
            throw new pix_errors_1.ValidationError('receiverName', `Receiver name cannot exceed ${PixTransaction.MAX_RECEIVER_NAME_LENGTH} characters`);
        }
        // Validate receiver city
        if (!receiverCity.trim()) {
            throw new pix_errors_1.ValidationError('receiverCity', 'Receiver city is required');
        }
        if (receiverCity.length > PixTransaction.MAX_RECEIVER_CITY_LENGTH) {
            throw new pix_errors_1.ValidationError('receiverCity', `Receiver city cannot exceed ${PixTransaction.MAX_RECEIVER_CITY_LENGTH} characters`);
        }
        return new PixTransaction(pixKey, receiverName.trim(), receiverCity.trim(), amount, reference?.trim(), description?.trim());
    }
    get pixKey() {
        return this._pixKey;
    }
    get receiverName() {
        return this._receiverName;
    }
    get receiverCity() {
        return this._receiverCity;
    }
    get amount() {
        return this._amount;
    }
    get reference() {
        return this._reference;
    }
    get description() {
        return this._description;
    }
    /**
     * Check if this is a free amount transaction (amount is zero)
     */
    isFreeAmount() {
        return this._amount.isZero();
    }
    /**
     * Get transaction display details
     */
    getDisplayDetails() {
        const details = [
            { label: 'Recebedor', value: this._receiverName },
            { label: 'Cidade', value: this._receiverCity },
            { label: 'Chave PIX', value: this._pixKey.getFormattedValue() },
            {
                label: 'Valor',
                value: this.isFreeAmount() ? 'Valor livre' : this._amount.toBrazilianCurrency()
            }
        ];
        if (this._reference) {
            details.push({ label: 'Referência', value: this._reference });
        }
        if (this._description) {
            details.push({ label: 'Descrição', value: this._description });
        }
        return details;
    }
}
exports.PixTransaction = PixTransaction;
PixTransaction.MAX_RECEIVER_NAME_LENGTH = 25;
PixTransaction.MAX_RECEIVER_CITY_LENGTH = 15;
//# sourceMappingURL=pix-transaction.js.map