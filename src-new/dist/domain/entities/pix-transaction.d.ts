/**
 * PIX Transaction Entity
 * Core business entity representing a PIX transaction
 */
import { <PERSON>x<PERSON><PERSON> } from '../value-objects/pix-key';
import { Money } from '../value-objects/money';
export declare class PixTransaction {
    private readonly _pixKey;
    private readonly _receiverName;
    private readonly _receiverCity;
    private readonly _amount;
    private readonly _reference?;
    private readonly _description?;
    private static readonly MAX_RECEIVER_NAME_LENGTH;
    private static readonly MAX_RECEIVER_CITY_LENGTH;
    private constructor();
    static create(params: {
        pixKey: PixKey;
        receiverName: string;
        receiverCity: string;
        amount: Money;
        reference?: string;
        description?: string;
    }): PixTransaction;
    get pixKey(): PixKey;
    get receiverName(): string;
    get receiverCity(): string;
    get amount(): Money;
    get reference(): string | undefined;
    get description(): string | undefined;
    /**
     * Check if this is a free amount transaction (amount is zero)
     */
    isFreeAmount(): boolean;
    /**
     * Get transaction display details
     */
    getDisplayDetails(): Array<{
        label: string;
        value: string;
    }>;
}
//# sourceMappingURL=pix-transaction.d.ts.map