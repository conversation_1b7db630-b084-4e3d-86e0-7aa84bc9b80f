"use strict";
/**
 * PIX Key Value Object
 * Encapsulates PIX key validation and formatting logic
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PixKey = void 0;
const pix_errors_1 = require("../../shared/errors/pix-errors");
class PixKey {
    constructor(_value, _type) {
        this._value = _value;
        this._type = _type;
    }
    static create(value, type) {
        const normalizedValue = this.normalize(value, type);
        if (!this.validate(normalizedValue, type)) {
            throw new pix_errors_1.ValidationError('pixKey', `Invalid ${type} format`);
        }
        return new PixKey(normalizedValue, type);
    }
    static normalize(value, type) {
        switch (type) {
            case 'cpf':
                return value.replace(/\D/g, '');
            case 'phone':
                const digits = value.replace(/\D/g, '');
                if (!digits.startsWith('55')) {
                    return `+55${digits}`;
                }
                return digits.startsWith('+') ? digits : `+${digits}`;
            case 'email':
            case 'random':
                return value.trim();
            default:
                throw new pix_errors_1.ValidationError('pix<PERSON>ey', `Unknown PIX key type: ${type}`);
        }
    }
    static validate(value, type) {
        switch (type) {
            case 'cpf':
                return this.validateCPF(value);
            case 'phone':
                return this.validatePhone(value);
            case 'email':
                return this.validateEmail(value);
            case 'random':
                return value.length >= 10;
            default:
                return false;
        }
    }
    static validateCPF(cpf) {
        if (cpf.length !== 11)
            return false;
        if (/^(\d)\1{10}$/.test(cpf))
            return false;
        let sum = 0;
        for (let i = 0; i < 9; i++) {
            sum += parseInt(cpf.charAt(i)) * (10 - i);
        }
        let digit1 = ((sum * 10) % 11) % 10;
        if (digit1 !== parseInt(cpf.charAt(9)))
            return false;
        sum = 0;
        for (let i = 0; i < 10; i++) {
            sum += parseInt(cpf.charAt(i)) * (11 - i);
        }
        let digit2 = ((sum * 10) % 11) % 10;
        return digit2 === parseInt(cpf.charAt(10));
    }
    static validatePhone(phone) {
        const digits = phone.replace(/\D/g, '');
        return digits.length === 13 && digits.startsWith('55'); // +55 + 11 digits
    }
    static validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    get value() {
        return this._value;
    }
    get type() {
        return this._type;
    }
    /**
     * Returns formatted value for display
     */
    getFormattedValue() {
        switch (this._type) {
            case 'cpf':
                return this._value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
            case 'phone':
                const digits = this._value.replace(/\D/g, '');
                if (digits.length === 13) {
                    return `+55 (${digits.substring(2, 4)}) ${digits.substring(4, 9)}-${digits.substring(9)}`;
                }
                return this._value;
            default:
                return this._value;
        }
    }
    equals(other) {
        return this._value === other._value && this._type === other._type;
    }
}
exports.PixKey = PixKey;
//# sourceMappingURL=pix-key.js.map