/**
 * PIX Key Value Object
 * Encapsulates PIX key validation and formatting logic
 */
import { PixKeyType } from '../../shared/types/pix-types';
export declare class PixKey {
    private readonly _value;
    private readonly _type;
    private constructor();
    static create(value: string, type: PixKeyType): PixKey;
    private static normalize;
    private static validate;
    private static validateCPF;
    private static validatePhone;
    private static validateEmail;
    get value(): string;
    get type(): PixKeyType;
    /**
     * Returns formatted value for display
     */
    getFormattedValue(): string;
    equals(other: PixKey): boolean;
}
//# sourceMappingURL=pix-key.d.ts.map