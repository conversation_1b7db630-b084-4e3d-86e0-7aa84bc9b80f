/**
 * Money Value Object
 * Handles monetary values with proper validation and formatting
 */
export declare class Money {
    private readonly _amount;
    private static readonly MAX_DIGITS;
    private constructor();
    static create(amount: number): Money;
    static zero(): Money;
    get amount(): number;
    getValue(): number;
    /**
     * Returns Brazilian currency formatted string
     */
    toBrazilianCurrency(): string;
    /**
     * Returns decimal formatted string for BR Code
     */
    toDecimalString(): string;
    isZero(): boolean;
    equals(other: Money): boolean;
    add(other: Money): Money;
    subtract(other: Money): Money;
}
//# sourceMappingURL=money.d.ts.map