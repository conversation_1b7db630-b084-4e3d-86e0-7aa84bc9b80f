"use strict";
/**
 * Money Value Object
 * Handles monetary values with proper validation and formatting
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Money = void 0;
const pix_errors_1 = require("../../shared/errors/pix-errors");
class Money {
    constructor(_amount) {
        this._amount = _amount;
    }
    static create(amount) {
        if (amount < 0) {
            throw new pix_errors_1.ValidationError('amount', 'Amount cannot be negative');
        }
        const formattedValue = amount.toFixed(2);
        if (formattedValue.length > Money.MAX_DIGITS) {
            throw new pix_errors_1.ValidationError('amount', `Amount exceeds maximum of ${Money.MAX_DIGITS} characters`);
        }
        return new Money(amount);
    }
    static zero() {
        return new Money(0);
    }
    get amount() {
        return this._amount;
    }
    getValue() {
        return this._amount;
    }
    /**
     * Returns Brazilian currency formatted string
     */
    toBrazilianCurrency() {
        return `R$ ${this._amount.toFixed(2).replace('.', ',')}`;
    }
    /**
     * Returns decimal formatted string for BR Code
     */
    toDecimalString() {
        return this._amount.toFixed(2);
    }
    isZero() {
        return this._amount === 0;
    }
    equals(other) {
        return Math.abs(this._amount - other._amount) < 0.001; // Handle floating point precision
    }
    add(other) {
        return Money.create(this._amount + other._amount);
    }
    subtract(other) {
        return Money.create(this._amount - other._amount);
    }
}
exports.Money = Money;
Money.MAX_DIGITS = 13; // Including decimals
//# sourceMappingURL=money.js.map