export let preset: string;
export let testEnvironment: string;
export let testMatch: string[];
export let setupFilesAfterEnv: string[];
export let collectCoverage: boolean;
export let coverageDirectory: string;
export let coverageReporters: string[];
export let collectCoverageFrom: string[];
export let coverageThreshold: {
    global: {
        branches: number;
        functions: number;
        lines: number;
        statements: number;
    };
    './domain/': {
        branches: number;
        functions: number;
        lines: number;
        statements: number;
    };
};
export let moduleNameMapping: {
    '^@shared/(.*)$': string;
    '^@domain/(.*)$': string;
    '^@application/(.*)$': string;
    '^@infrastructure/(.*)$': string;
    '^@presentation/(.*)$': string;
};
export let transform: {
    '^.+\\.ts$': string;
};
export let moduleFileExtensions: string[];
export namespace testEnvironmentOptions {
    let url: string;
}
export let clearMocks: boolean;
export let restoreMocks: boolean;
export let testTimeout: number;
export let verbose: boolean;
export let globals: {
    'ts-jest': {
        useESM: boolean;
        tsconfig: {
            compilerOptions: {
                target: string;
                module: string;
            };
        };
    };
};
//# sourceMappingURL=jest.config.d.ts.map