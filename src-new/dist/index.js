"use strict";
/**
 * Main entry point for the refactored QR Code Styling library
 * Exports the public API following Clean Architecture principles
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PixController = exports.createQRCodeRepository = exports.QRCodeRepositoryImpl = exports.GeneratePixQRCodeUseCase = exports.BRCodeGenerator = exports.PixTransaction = exports.Money = exports.PixKey = void 0;
exports.createPixQRCodeService = createPixQRCodeService;
// Domain exports (core business logic)
var pix_key_1 = require("./domain/value-objects/pix-key");
Object.defineProperty(exports, "PixKey", { enumerable: true, get: function () { return pix_key_1.PixKey; } });
var money_1 = require("./domain/value-objects/money");
Object.defineProperty(exports, "Money", { enumerable: true, get: function () { return money_1.Money; } });
var pix_transaction_1 = require("./domain/entities/pix-transaction");
Object.defineProperty(exports, "PixTransaction", { enumerable: true, get: function () { return pix_transaction_1.PixTransaction; } });
var br_code_generator_1 = require("./domain/services/br-code-generator");
Object.defineProperty(exports, "BRCodeGenerator", { enumerable: true, get: function () { return br_code_generator_1.BRCodeGenerator; } });
// Application exports (use cases and DTOs)
var generate_pix_qrcode_1 = require("./application/usecases/generate-pix-qrcode");
Object.defineProperty(exports, "GeneratePixQRCodeUseCase", { enumerable: true, get: function () { return generate_pix_qrcode_1.GeneratePixQRCodeUseCase; } });
// Infrastructure exports (implementations)
const qr_code_repository_impl_1 = require("./infrastructure/repositories/qr-code-repository-impl");
const generate_pix_qrcode_2 = require("./application/usecases/generate-pix-qrcode");
const pix_controller_1 = require("./presentation/controllers/pix-controller");
var qr_code_repository_impl_2 = require("./infrastructure/repositories/qr-code-repository-impl");
Object.defineProperty(exports, "QRCodeRepositoryImpl", { enumerable: true, get: function () { return qr_code_repository_impl_2.QRCodeRepositoryImpl; } });
Object.defineProperty(exports, "createQRCodeRepository", { enumerable: true, get: function () { return qr_code_repository_impl_2.createQRCodeRepository; } });
// Presentation exports (controllers)
var pix_controller_2 = require("./presentation/controllers/pix-controller");
Object.defineProperty(exports, "PixController", { enumerable: true, get: function () { return pix_controller_2.PixController; } });
// Shared exports (types and utilities)
__exportStar(require("./shared/types/pix-types"), exports);
__exportStar(require("./shared/errors/pix-errors"), exports);
// Factory function for easy setup
function createPixQRCodeService() {
    const qrCodeRepository = (0, qr_code_repository_impl_1.createQRCodeRepository)();
    const generatePixQRCodeUseCase = new generate_pix_qrcode_2.GeneratePixQRCodeUseCase(qrCodeRepository);
    const pixController = new pix_controller_1.PixController(generatePixQRCodeUseCase);
    return {
        controller: pixController,
        useCase: generatePixQRCodeUseCase,
        repository: qrCodeRepository
    };
}
//# sourceMappingURL=index.js.map