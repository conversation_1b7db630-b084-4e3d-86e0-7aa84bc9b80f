"use strict";
module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'jsdom',
    // Test file patterns
    testMatch: [
        '<rootDir>/**/*.test.ts',
        '<rootDir>/**/*.spec.ts'
    ],
    // Setup files
    setupFilesAfterEnv: ['<rootDir>/test-setup.ts'],
    // Coverage configuration
    collectCoverage: true,
    coverageDirectory: 'coverage',
    coverageReporters: ['text', 'lcov', 'html', 'json'],
    collectCoverageFrom: [
        'src/**/*.ts',
        '!src/**/*.d.ts',
        '!src/**/*.test.ts',
        '!src/**/*.spec.ts',
        '!src/**/index.ts'
    ],
    // Coverage thresholds (enforce high quality)
    coverageThreshold: {
        global: {
            branches: 90,
            functions: 90,
            lines: 90,
            statements: 90
        },
        // Domain layer should have 100% coverage
        './domain/': {
            branches: 100,
            functions: 100,
            lines: 100,
            statements: 100
        }
    },
    // Module name mapping for path aliases
    moduleNameMapping: {
        '^@shared/(.*)$': '<rootDir>/shared/$1',
        '^@domain/(.*)$': '<rootDir>/domain/$1',
        '^@application/(.*)$': '<rootDir>/application/$1',
        '^@infrastructure/(.*)$': '<rootDir>/infrastructure/$1',
        '^@presentation/(.*)$': '<rootDir>/presentation/$1'
    },
    // Transform configuration
    transform: {
        '^.+\\.ts$': 'ts-jest'
    },
    // Module file extensions
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
    // Test environment options
    testEnvironmentOptions: {
        url: 'http://localhost'
    },
    // Clear mocks between tests
    clearMocks: true,
    restoreMocks: true,
    // Timeout configuration
    testTimeout: 10000,
    // Verbose output for debugging
    verbose: true,
    // Globals for ts-jest
    globals: {
        'ts-jest': {
            useESM: false,
            tsconfig: {
                compilerOptions: {
                    target: 'ES2020',
                    module: 'commonjs'
                }
            }
        }
    }
};
//# sourceMappingURL=jest.config.js.map