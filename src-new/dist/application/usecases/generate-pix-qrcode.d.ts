/**
 * Generate PIX QR Code Use Case
 * Orchestrates the generation of PIX QR codes with customization options
 */
import { PixTransaction } from '../../domain/entities/pix-transaction';
import { QRCustomizationOptions, QRGenerationOptions } from '../../shared/types/pix-types';
export interface GeneratePixQRCodeRequest {
    transaction: PixTransaction;
    customization?: QRCustomizationOptions;
    isDynamic?: boolean;
}
export interface GeneratePixQRCodeResponse {
    brCode: string;
    qrCodeDataUrl?: string;
    transaction: PixTransaction;
}
export interface QRCodeRepository {
    generateQRCode(options: QRGenerationOptions): Promise<string>;
    generateZeroMarginQRCode(options: QRGenerationOptions): Promise<string>;
}
export declare class GeneratePixQRCodeUseCase {
    private qrCodeRepository;
    constructor(qrCodeRepository: QRCodeRepository);
    execute(request: GeneratePixQRCodeRequest): Promise<GeneratePixQRCodeResponse>;
    private buildQROptions;
}
//# sourceMappingURL=generate-pix-qrcode.d.ts.map