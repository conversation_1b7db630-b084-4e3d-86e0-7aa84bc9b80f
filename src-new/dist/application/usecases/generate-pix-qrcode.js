"use strict";
/**
 * Generate PIX QR Code Use Case
 * Orchestrates the generation of PIX QR codes with customization options
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeneratePixQRCodeUseCase = void 0;
const br_code_generator_1 = require("../../domain/services/br-code-generator");
const pix_errors_1 = require("../../shared/errors/pix-errors");
class GeneratePixQRCodeUseCase {
    constructor(qrCodeRepository) {
        this.qrCodeRepository = qrCodeRepository;
    }
    async execute(request) {
        try {
            // Generate BR Code using domain service
            const brCode = br_code_generator_1.BRCodeGenerator.generate(request.transaction, request.isDynamic || false);
            let qrCodeDataUrl;
            if (request.customization) {
                // Generate customized QR code
                const qrOptions = this.buildQROptions(brCode, request.customization);
                if (request.customization.imageMargin === 0) {
                    qrCodeDataUrl = await this.qrCodeRepository.generateZeroMarginQRCode(qrOptions);
                }
                else {
                    qrCodeDataUrl = await this.qrCodeRepository.generateQRCode(qrOptions);
                }
            }
            const response = {
                brCode,
                transaction: request.transaction
            };
            if (qrCodeDataUrl) {
                response.qrCodeDataUrl = qrCodeDataUrl;
            }
            return response;
        }
        catch (error) {
            if (error instanceof pix_errors_1.QRGenerationError) {
                throw error;
            }
            throw new pix_errors_1.QRGenerationError(error instanceof Error ? error.message : 'Unknown error occurred');
        }
    }
    buildQROptions(brCode, customization) {
        return {
            width: customization.qrSize,
            height: customization.qrSize,
            type: 'canvas',
            data: brCode,
            margin: customization.imageMargin,
            qrOptions: {
                typeNumber: 0, // Auto-detect
                mode: undefined,
                errorCorrectionLevel: 'M' // Standard level for consistency
            },
            dotsOptions: {
                color: customization.dotsColor,
                type: customization.dotsType,
                roundSize: true
            },
            backgroundOptions: {
                color: customization.backgroundColor,
                round: 0
            },
            cornersSquareOptions: {
                color: customization.cornerSquareColor,
                type: customization.cornerSquareType
            },
            cornersDotOptions: {
                color: customization.cornerDotColor,
                type: customization.cornerDotType
            }
        };
    }
}
exports.GeneratePixQRCodeUseCase = GeneratePixQRCodeUseCase;
//# sourceMappingURL=generate-pix-qrcode.js.map