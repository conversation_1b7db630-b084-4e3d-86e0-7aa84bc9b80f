/**
 * Main entry point for the refactored QR Code Styling library
 * Exports the public API following Clean Architecture principles
 */
export { <PERSON>x<PERSON><PERSON> } from './domain/value-objects/pix-key';
export { Money } from './domain/value-objects/money';
export { PixTransaction } from './domain/entities/pix-transaction';
export { BRCodeGenerator } from './domain/services/br-code-generator';
export { GeneratePixQRCodeUseCase, type GeneratePixQRCodeRequest, type GeneratePixQRCodeResponse, type QRCodeRepository } from './application/usecases/generate-pix-qrcode';
import { GeneratePixQRCodeUseCase } from './application/usecases/generate-pix-qrcode';
import { PixController } from './presentation/controllers/pix-controller';
export { QRCodeRepositoryImpl, createQRCodeRepository } from './infrastructure/repositories/qr-code-repository-impl';
export { Pix<PERSON>ontroller, type PixControllerRequest, type PixControllerResponse } from './presentation/controllers/pix-controller';
export * from './shared/types/pix-types';
export * from './shared/errors/pix-errors';
export declare function createPixQRCodeService(): {
    controller: PixController;
    useCase: GeneratePixQRCodeUseCase;
    repository: import("./application/usecases/generate-pix-qrcode").QRCodeRepository;
};
//# sourceMappingURL=index.d.ts.map