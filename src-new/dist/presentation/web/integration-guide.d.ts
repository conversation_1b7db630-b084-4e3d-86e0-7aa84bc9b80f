/**
 * Integration Guide for Legacy HTML
 * Provides easy migration path from the old app.js to the new architecture
 */
import { AppController } from './app-controller';
/**
 * Automatically initializes the new architecture with existing HTML elements
 * This provides a drop-in replacement for the old PixQRGeneratorIntegrated class
 */
export declare function initializeFromHTML(): AppController;
/**
 * Legacy compatibility function
 * Maintains the same global interface as the original app.js
 */
export declare function initializeLegacyCompatibility(): void;
/**
 * Development helper to check element availability
 */
export declare function checkHTMLCompatibility(): {
    compatible: boolean;
    missing: string[];
    suggestions: string[];
};
//# sourceMappingURL=integration-guide.d.ts.map