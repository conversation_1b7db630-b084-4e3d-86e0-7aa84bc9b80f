"use strict";
/**
 * QR Display Component
 * Manages QR code display, downloads, and interactions
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.QRDisplayComponent = void 0;
const file_operations_1 = require("../../../infrastructure/file-system/file-operations");
class QRDisplayComponent {
    constructor(elements, callbacks) {
        this.currentData = null;
        this.elements = elements;
        this.callbacks = callbacks;
        this.fileService = new file_operations_1.FileOperationsService();
        this.setupEventListeners();
    }
    setupEventListeners() {
        // Download buttons
        this.elements.downloadBtn.addEventListener('click', () => {
            this.handleDownload('png');
        });
        this.elements.downloadSvgBtn.addEventListener('click', () => {
            this.handleDownload('svg');
        });
        // Copy BR Code button
        this.elements.copyBtn.addEventListener('click', () => {
            this.handleCopyBRCode();
        });
    }
    displayQRResult(data) {
        this.currentData = data;
        // Hide placeholder and show result
        this.elements.qrPlaceholder.style.display = 'none';
        this.elements.qrResult.style.display = 'block';
        // Clear previous content
        this.elements.qrPreview.innerHTML = '';
        // Display QR code
        if (data.qrCodeElement) {
            this.elements.qrPreview.appendChild(data.qrCodeElement);
        }
        else if (data.qrCodeDataUrl) {
            this.displayQRFromDataUrl(data.qrCodeDataUrl);
        }
        // Set BR Code text
        this.elements.brCodeText.textContent = data.brCode;
        // Display PIX details
        this.displayPixDetails(data.transaction);
        // Scroll to result
        this.elements.qrResult.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    }
    displayQRFromDataUrl(dataUrl) {
        const img = document.createElement('img');
        img.src = dataUrl;
        img.alt = 'QR Code PIX';
        img.style.maxWidth = '100%';
        img.style.borderRadius = '8px';
        this.elements.qrPreview.appendChild(img);
    }
    displayPixDetails(transaction) {
        const details = transaction.getDisplayDetails();
        const detailsHTML = details
            .map(item => `
        <div class="pix-detail-item">
          <span class="pix-detail-label">${item.label}:</span>
          <span class="pix-detail-value">${item.value}</span>
        </div>
      `)
            .join('');
        this.elements.pixDetails.innerHTML = detailsHTML;
    }
    async handleDownload(format) {
        if (!this.currentData) {
            this.callbacks.showToast('Nenhum QR Code para download', 'error');
            return;
        }
        try {
            await this.callbacks.onDownloadRequest(format);
            this.callbacks.showToast(`QR Code baixado como ${format.toUpperCase()}!`, 'success');
        }
        catch (error) {
            console.error('Download error:', error);
            this.callbacks.showToast('Erro ao fazer download do QR Code.', 'error');
        }
    }
    async handleCopyBRCode() {
        if (!this.currentData) {
            this.callbacks.showToast('Nenhum BR Code para copiar', 'error');
            return;
        }
        try {
            await this.callbacks.onCopyBRCode(this.currentData.brCode);
            this.callbacks.showToast('BR Code copiado para a área de transferência!', 'success');
        }
        catch (error) {
            console.error('Copy error:', error);
            this.callbacks.showToast('Erro ao copiar BR Code', 'error');
        }
    }
    setLoading(loading) {
        if (loading) {
            this.elements.loadingSpinner.style.display = 'block';
            this.elements.qrResult.style.display = 'none';
            this.elements.qrPlaceholder.style.display = 'none';
        }
        else {
            this.elements.loadingSpinner.style.display = 'none';
            if (this.currentData) {
                this.elements.qrResult.style.display = 'block';
            }
            else {
                this.elements.qrPlaceholder.style.display = 'block';
            }
        }
    }
    hideResult() {
        this.elements.qrResult.style.display = 'none';
        this.elements.qrPlaceholder.style.display = 'block';
        this.elements.qrPreview.innerHTML = '';
        this.currentData = null;
    }
    getCurrentBRCode() {
        return this.currentData?.brCode || null;
    }
    async downloadCurrentQR(options) {
        if (!this.currentData?.qrCodeDataUrl) {
            throw new Error('No QR code data available for download');
        }
        await this.fileService.downloadFile(this.currentData.qrCodeDataUrl, options);
    }
    // Utility methods for different QR display scenarios
    displayStandardQR(brCode, transaction) {
        this.displayQRResult({
            brCode,
            transaction
        });
    }
    displayCustomizedQR(brCode, transaction, qrElement) {
        this.displayQRResult({
            brCode,
            transaction,
            qrCodeElement: qrElement
        });
    }
    updateMarginStyles(margin) {
        const classList = this.elements.qrPreview.classList;
        // Remove existing margin classes
        classList.remove('margin-none', 'margin-default', 'margin-wide');
        // Add appropriate margin class
        switch (margin) {
            case 0:
                classList.add('margin-none');
                break;
            case 10:
                classList.add('margin-default');
                break;
            case 30:
                classList.add('margin-wide');
                break;
        }
    }
    getQRPreviewElement() {
        return this.elements.qrPreview;
    }
    // Animation and visual feedback methods
    showGeneratingAnimation() {
        this.elements.qrPreview.innerHTML = `
      <div class="generating-animation">
        <div class="qr-skeleton"></div>
        <p>Gerando QR Code...</p>
      </div>
    `;
    }
    showErrorState(message) {
        this.elements.qrPreview.innerHTML = `
      <div class="error-state">
        <div class="error-icon">⚠️</div>
        <p>${message}</p>
        <button class="retry-btn">Tentar novamente</button>
      </div>
    `;
        // Add retry functionality
        const retryBtn = this.elements.qrPreview.querySelector('.retry-btn');
        if (retryBtn) {
            retryBtn.addEventListener('click', () => {
                this.hideResult();
            });
        }
    }
}
exports.QRDisplayComponent = QRDisplayComponent;
//# sourceMappingURL=qr-display-component.js.map