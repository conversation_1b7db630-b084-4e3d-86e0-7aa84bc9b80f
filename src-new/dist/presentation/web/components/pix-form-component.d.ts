/**
 * PIX Form Component
 * Manages form state, validation, and user interactions
 */
import { PixFormData } from '../../../shared/types/pix-types';
export interface PixFormElements {
    form: HTMLFormElement;
    keyTypeSelect: HTMLSelectElement;
    pixKeyInput: HTMLInputElement;
    receiverNameInput: HTMLInputElement;
    receiverCityInput: HTMLInputElement;
    amountInput: HTMLInputElement;
    referenceInput: HTMLInputElement;
    descriptionInput: HTMLTextAreaElement;
    generateBtn: HTMLButtonElement;
    keyValidation: HTMLElement;
}
export interface PixFormCallbacks {
    onFormSubmit: (data: PixFormData) => Promise<void>;
    onKeyValidation: (isValid: boolean, message: string) => void;
    onFormChange: (data: Partial<PixFormData>) => void;
}
export declare class PixFormComponent {
    private elements;
    private callbacks;
    private formData;
    constructor(elements: PixFormElements, callbacks: PixFormCallbacks);
    private setupEventListeners;
    private setupMasks;
    private setupValidation;
    private handleKeyTypeChange;
    private setupCPFMask;
    private setupPhoneMask;
    private cpfMaskHandler?;
    private phoneMaskHandler?;
    private removeMasks;
    private validatePixKey;
    private showValidation;
    private clearValidation;
    private getSuccessMessage;
    private getErrorMessage;
    private updateCharCounter;
    private handleFormChange;
    private handleFormSubmit;
    private validateForm;
    private showFieldError;
    private parseAmount;
    setLoading(loading: boolean): void;
    reset(): void;
    getFormData(): PixFormData;
}
//# sourceMappingURL=pix-form-component.d.ts.map