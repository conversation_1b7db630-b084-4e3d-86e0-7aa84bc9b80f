/**
 * QR Customization Component
 * Manages QR code styling and customization options
 */
import { QRCustomizationOptions } from '../../../shared/types/pix-types';
export interface QRCustomizationElements {
    customizationToggle: HTMLButtonElement;
    customizationPanel: HTMLElement;
    dotsType: HTMLSelectElement;
    dotsColor: HTMLInputElement;
    cornerSquareType: HTMLSelectElement;
    cornerSquareColor: HTMLInputElement;
    cornerDotType: HTMLSelectElement;
    cornerDotColor: HTMLInputElement;
    backgroundColor: HTMLInputElement;
    qrSize: HTMLInputElement;
    imageSize: HTMLInputElement;
    imageSizeValue: HTMLElement;
    imageMargin: HTMLInputElement;
    hideBackgroundDots: HTMLInputElement;
    centerImage: HTMLInputElement;
    marginButtons: NodeListOf<HTMLButtonElement>;
}
export interface QRCustomizationCallbacks {
    onCustomizationChange: (options: QRCustomizationOptions) => void;
    onToggleCustomization: (active: boolean) => void;
    onImageUpload: (file: File) => void;
    onMarginChange: (margin: number) => void;
}
export declare class QRCustomizationComponent {
    private elements;
    private callbacks;
    private isActive;
    private currentImageFile;
    constructor(elements: QRCustomizationElements, callbacks: QRCustomizationCallbacks);
    private setupEventListeners;
    private initializeValues;
    private toggleCustomization;
    private handleCustomizationChange;
    private handleMarginButtonClick;
    private handleImageUpload;
    private handleSizeChange;
    private updateRangeValue;
    private getCurrentCustomization;
    private setActiveMarginButton;
    applyPreset(presetName: string): void;
    private applyPresetValues;
    getCurrentMargin(): number;
    isCustomizationActive(): boolean;
    getCurrentImageFile(): File | null;
    reset(): void;
}
//# sourceMappingURL=qr-customization-component.d.ts.map