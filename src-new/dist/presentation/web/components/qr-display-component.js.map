{"version": 3, "file": "qr-display-component.js", "sourceRoot": "", "sources": ["../../../../presentation/web/components/qr-display-component.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,yFAA6G;AA2B7G,MAAa,kBAAkB;IAM7B,YAAY,QAA2B,EAAE,SAA6B;QAF9D,gBAAW,GAAyB,IAAI,CAAC;QAG/C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,IAAI,uCAAqB,EAAE,CAAC;QAC/C,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB;QACzB,mBAAmB;QACnB,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACvD,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YAC1D,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,eAAe,CAAC,IAAmB;QACxC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,mCAAmC;QACnC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QACnD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QAE/C,yBAAyB;QACzB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC;QAEvC,kBAAkB;QAClB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC1D,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC9B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAChD,CAAC;QAED,mBAAmB;QACnB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;QAEnD,sBAAsB;QACtB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAEzC,mBAAmB;QACnB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC;YACpC,QAAQ,EAAE,QAAQ;YAClB,KAAK,EAAE,QAAQ;SAChB,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,OAAe;QAC1C,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1C,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC;QAClB,GAAG,CAAC,GAAG,GAAG,aAAa,CAAC;QACxB,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;QAC5B,GAAG,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;QAC/B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC;IAEO,iBAAiB,CAAC,WAA2B;QACnD,MAAM,OAAO,GAAG,WAAW,CAAC,iBAAiB,EAAE,CAAC;QAEhD,MAAM,WAAW,GAAG,OAAO;aACxB,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;;2CAEwB,IAAI,CAAC,KAAK;2CACV,IAAI,CAAC,KAAK;;OAE9C,CAAC;aACD,IAAI,CAAC,EAAE,CAAC,CAAC;QAEZ,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,GAAG,WAAW,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAqB;QAChD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,8BAA8B,EAAE,OAAO,CAAC,CAAC;YAClE,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAC/C,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,wBAAwB,MAAM,CAAC,WAAW,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;QACvF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;YACxC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,oCAAoC,EAAE,OAAO,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;YAChE,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAC3D,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,+CAA+C,EAAE,SAAS,CAAC,CAAC;QACvF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YACpC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,wBAAwB,EAAE,OAAO,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEM,UAAU,CAAC,OAAgB;QAChC,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;YACrD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;YACpD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;YACtD,CAAC;QACH,CAAC;IACH,CAAC;IAEM,UAAU;QACf,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QAC9C,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QACpD,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAEM,gBAAgB;QACrB,OAAO,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,IAAI,CAAC;IAC1C,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,OAAwB;QACrD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IAC/E,CAAC;IAED,qDAAqD;IAC9C,iBAAiB,CAAC,MAAc,EAAE,WAA2B;QAClE,IAAI,CAAC,eAAe,CAAC;YACnB,MAAM;YACN,WAAW;SACZ,CAAC,CAAC;IACL,CAAC;IAEM,mBAAmB,CACxB,MAAc,EACd,WAA2B,EAC3B,SAAsB;QAEtB,IAAI,CAAC,eAAe,CAAC;YACnB,MAAM;YACN,WAAW;YACX,aAAa,EAAE,SAAS;SACzB,CAAC,CAAC;IACL,CAAC;IAEM,kBAAkB,CAAC,MAAc;QACtC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC;QAEpD,iCAAiC;QACjC,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAEjE,+BAA+B;QAC/B,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,CAAC;gBACJ,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAC7B,MAAM;YACR,KAAK,EAAE;gBACL,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;gBAChC,MAAM;YACR,KAAK,EAAE;gBACL,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAC7B,MAAM;QACV,CAAC;IACH,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;IACjC,CAAC;IAED,wCAAwC;IACjC,uBAAuB;QAC5B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG;;;;;KAKnC,CAAC;IACJ,CAAC;IAEM,cAAc,CAAC,OAAe;QACnC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG;;;aAG3B,OAAO;;;KAGf,CAAC;QAEF,0BAA0B;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;QACrE,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;gBACtC,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AA1ND,gDA0NC"}