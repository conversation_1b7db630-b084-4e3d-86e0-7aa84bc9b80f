"use strict";
/**
 * PIX Form Component
 * Manages form state, validation, and user interactions
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PixFormComponent = void 0;
const pix_key_1 = require("../../../domain/value-objects/pix-key");
const pix_errors_1 = require("../../../shared/errors/pix-errors");
class PixFormComponent {
    constructor(elements, callbacks) {
        this.formData = {};
        this.elements = elements;
        this.callbacks = callbacks;
        this.setupEventListeners();
        this.setupMasks();
        this.setupValidation();
    }
    setupEventListeners() {
        // Form submission
        this.elements.form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        // Key type change
        this.elements.keyTypeSelect.addEventListener('change', () => this.handleKeyTypeChange());
        // PIX key input validation
        this.elements.pixKeyInput.addEventListener('input', () => this.validatePixKey());
        this.elements.pixKeyInput.addEventListener('blur', () => this.validatePixKey());
        // Character counters
        this.elements.receiverNameInput.addEventListener('input', () => this.updateCharCounter(this.elements.receiverNameInput, 25));
        this.elements.receiverCityInput.addEventListener('input', () => this.updateCharCounter(this.elements.receiverCityInput, 15));
        // Form changes
        this.elements.form.addEventListener('input', () => this.handleFormChange());
    }
    setupMasks() {
        // Currency mask for amount input
        this.elements.amountInput.addEventListener('input', (e) => {
            const target = e.target;
            let value = target.value.replace(/\\D/g, '');
            if (value.length === 0) {
                target.value = '';
                return;
            }
            const numericValue = (parseInt(value) / 100).toFixed(2);
            target.value = 'R$ ' + numericValue.replace('.', ',');
        });
        // Reference input: only alphanumeric
        this.elements.referenceInput.addEventListener('input', (e) => {
            const target = e.target;
            target.value = target.value.replace(/[^A-Za-z0-9]/g, '');
        });
    }
    setupValidation() {
        this.handleKeyTypeChange();
    }
    handleKeyTypeChange() {
        const keyType = this.elements.keyTypeSelect.value;
        const pixKeyInput = this.elements.pixKeyInput;
        // Clear previous value and validation
        pixKeyInput.value = '';
        this.clearValidation();
        // Update placeholder and input type based on key type
        switch (keyType) {
            case 'cpf':
                pixKeyInput.placeholder = '000.000.000-00';
                pixKeyInput.type = 'text';
                pixKeyInput.maxLength = 14;
                this.setupCPFMask();
                break;
            case 'phone':
                pixKeyInput.placeholder = '(11) 99999-9999';
                pixKeyInput.type = 'tel';
                pixKeyInput.maxLength = 15;
                this.setupPhoneMask();
                break;
            case 'email':
                pixKeyInput.placeholder = '<EMAIL>';
                pixKeyInput.type = 'email';
                pixKeyInput.maxLength = 50;
                this.removeMasks();
                break;
            case 'random':
                pixKeyInput.placeholder = 'chave-aleatoria-uuid';
                pixKeyInput.type = 'text';
                pixKeyInput.maxLength = 50;
                this.removeMasks();
                break;
        }
        this.formData.keyType = keyType;
        this.callbacks.onFormChange(this.formData);
    }
    setupCPFMask() {
        this.removeMasks();
        this.cpfMaskHandler = (e) => {
            const target = e.target;
            let value = target.value.replace(/\\D/g, '');
            value = value.replace(/(\\d{3})(\\d)/, '$1.$2');
            value = value.replace(/(\\d{3})(\\d)/, '$1.$2');
            value = value.replace(/(\\d{3})(\\d{1,2})$/, '$1-$2');
            target.value = value;
        };
        this.elements.pixKeyInput.addEventListener('input', this.cpfMaskHandler);
    }
    setupPhoneMask() {
        this.removeMasks();
        this.phoneMaskHandler = (e) => {
            const target = e.target;
            let value = target.value.replace(/\\D/g, '');
            if (value.length <= 10) {
                value = value.replace(/(\\d{2})(\\d)/, '($1) $2');
                value = value.replace(/(\\d{4})(\\d)/, '$1-$2');
            }
            else {
                value = value.replace(/(\\d{2})(\\d)/, '($1) $2');
                value = value.replace(/(\\d{5})(\\d)/, '$1-$2');
            }
            target.value = value;
        };
        this.elements.pixKeyInput.addEventListener('input', this.phoneMaskHandler);
    }
    removeMasks() {
        if (this.cpfMaskHandler) {
            this.elements.pixKeyInput.removeEventListener('input', this.cpfMaskHandler);
        }
        if (this.phoneMaskHandler) {
            this.elements.pixKeyInput.removeEventListener('input', this.phoneMaskHandler);
        }
    }
    validatePixKey() {
        const keyType = this.elements.keyTypeSelect.value;
        const value = this.elements.pixKeyInput.value.trim();
        if (!value) {
            this.clearValidation();
            return;
        }
        try {
            pix_key_1.PixKey.create(value, keyType);
            this.showValidation(true, this.getSuccessMessage(keyType));
            this.callbacks.onKeyValidation(true, this.getSuccessMessage(keyType));
        }
        catch (error) {
            if (error instanceof pix_errors_1.ValidationError) {
                this.showValidation(false, this.getErrorMessage(keyType));
                this.callbacks.onKeyValidation(false, this.getErrorMessage(keyType));
            }
        }
    }
    showValidation(isValid, message) {
        this.elements.keyValidation.textContent = message;
        this.elements.keyValidation.className = `validation-message ${isValid ? 'success' : 'error'}`;
    }
    clearValidation() {
        this.elements.keyValidation.textContent = '';
        this.elements.keyValidation.className = 'validation-message';
    }
    getSuccessMessage(keyType) {
        const messages = {
            cpf: 'CPF válido',
            phone: 'Telefone válido',
            email: 'Email válido',
            random: 'Chave válida'
        };
        return messages[keyType];
    }
    getErrorMessage(keyType) {
        const messages = {
            cpf: 'CPF inválido',
            phone: 'Telefone inválido',
            email: 'Email inválido',
            random: 'Chave deve ter pelo menos 10 caracteres'
        };
        return messages[keyType];
    }
    updateCharCounter(input, maxLength) {
        const counter = input.parentElement?.querySelector('.char-counter');
        if (counter) {
            counter.textContent = `${input.value.length}/${maxLength} caracteres`;
        }
    }
    handleFormChange() {
        this.formData = {
            keyType: this.elements.keyTypeSelect.value,
            pixKey: this.elements.pixKeyInput.value,
            receiverName: this.elements.receiverNameInput.value,
            receiverCity: this.elements.receiverCityInput.value,
            amount: this.parseAmount(this.elements.amountInput.value),
            reference: this.elements.referenceInput.value,
            description: this.elements.descriptionInput.value
        };
        this.callbacks.onFormChange(this.formData);
    }
    async handleFormSubmit(e) {
        e.preventDefault();
        if (!this.validateForm()) {
            return;
        }
        const formData = this.getFormData();
        await this.callbacks.onFormSubmit(formData);
    }
    validateForm() {
        const requiredFields = [
            { element: this.elements.pixKeyInput, name: 'Chave PIX' },
            { element: this.elements.receiverNameInput, name: 'Nome do recebedor' },
            { element: this.elements.receiverCityInput, name: 'Cidade do recebedor' }
        ];
        for (const field of requiredFields) {
            if (!field.element.value.trim()) {
                this.showFieldError(field.element, `O campo "${field.name}" é obrigatório.`);
                return false;
            }
        }
        // Validate PIX key using domain validation
        try {
            const keyType = this.elements.keyTypeSelect.value;
            pix_key_1.PixKey.create(this.elements.pixKeyInput.value, keyType);
        }
        catch (error) {
            this.showFieldError(this.elements.pixKeyInput, 'Por favor, insira uma chave PIX válida.');
            return false;
        }
        return true;
    }
    showFieldError(field, message) {
        // This would show field-specific error message
        // Implementation depends on UI framework
        field.focus();
        console.error(message);
    }
    parseAmount(amountStr) {
        if (!amountStr.trim())
            return 0;
        const cleanValue = amountStr.replace(/[^\\d,]/g, '').replace(',', '.');
        return parseFloat(cleanValue) || 0;
    }
    // Public methods for external control
    setLoading(loading) {
        this.elements.generateBtn.disabled = loading;
        this.elements.generateBtn.classList.toggle('loading', loading);
    }
    reset() {
        this.elements.form.reset();
        this.clearValidation();
        this.formData = {};
    }
    getFormData() {
        return {
            keyType: this.elements.keyTypeSelect.value,
            pixKey: this.elements.pixKeyInput.value.trim(),
            receiverName: this.elements.receiverNameInput.value.trim(),
            receiverCity: this.elements.receiverCityInput.value.trim(),
            amount: this.parseAmount(this.elements.amountInput.value),
            reference: this.elements.referenceInput.value.trim(),
            description: this.elements.descriptionInput.value.trim()
        };
    }
}
exports.PixFormComponent = PixFormComponent;
//# sourceMappingURL=pix-form-component.js.map