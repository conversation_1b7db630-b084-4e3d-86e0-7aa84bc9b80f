{"version": 3, "file": "qr-customization-component.js", "sourceRoot": "", "sources": ["../../../../presentation/web/components/qr-customization-component.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AA8BH,MAAa,wBAAwB;IAMnC,YAAY,QAAiC,EAAE,SAAmC;QAH1E,aAAQ,GAAG,KAAK,CAAC;QACjB,qBAAgB,GAAgB,IAAI,CAAC;QAG3C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,mBAAmB;QACzB,gBAAgB;QAChB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YAC/D,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,MAAM,mBAAmB,GAAG;YAC1B,UAAU,EAAE,WAAW,EAAE,kBAAkB,EAAE,mBAAmB;YAChE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ;YAC9D,WAAW,EAAE,aAAa,EAAE,oBAAoB;SACxC,CAAC;QAEX,mBAAmB,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YACjC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAClC,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC7B,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrE,CAAC;gBAED,IAAI,EAAE,KAAK,QAAQ,EAAE,CAAC;oBACpB,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtE,CAAC;gBAED,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;gBAC3E,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,iBAAiB;QACjB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC7C,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;gBACrC,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,eAAe;QACf,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE;YACzD,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;IACrC,CAAC;IAEO,gBAAgB;QACtB,yCAAyC;QACzC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACnC,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC,iBAAiB;IACnD,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC/B,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC5E,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE3E,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEpD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,yBAAyB;QAC/B,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE3B,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/C,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAEO,uBAAuB,CAAC,MAAyB;QACvD,uCAAuC;QACvC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE7E,qCAAqC;QACrC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE/B,uCAAuC;QACvC,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAEtC,+BAA+B;QAC/B,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAEO,iBAAiB,CAAC,KAAY;QACpC,MAAM,MAAM,GAAG,KAAK,CAAC,MAA0B,CAAC;QAChD,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QAE/B,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAEnC,oBAAoB;YACpB,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;YAC5D,IAAI,KAAK,EAAE,CAAC;gBACV,KAAK,CAAC,SAAS,GAAG,KAAK,IAAI,CAAC,IAAI,wCAAwC,CAAC;YAC3E,CAAC;YAED,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,KAAY;QACnC,MAAM,MAAM,GAAG,KAAK,CAAC,MAA0B,CAAC;QAChD,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAEpC,sDAAsD;QACtD,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAqB,CAAC;QAC3E,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAqB,CAAC;QAE7E,IAAI,UAAU;YAAE,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACnD,IAAI,WAAW;YAAE,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAErD,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAEO,gBAAgB,CAAC,OAAsC;QAC7D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAqB,CAAC;QACzD,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,IAAI,YAAY,GAAuB,IAAI,CAAC;QAC5C,IAAI,YAAY,GAAG,EAAE,CAAC;QAEtB,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,WAAW;gBACd,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;gBAC5C,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;gBAC/D,MAAM;QACV,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,WAAW,GAAG,YAAY,CAAC;QAC1C,CAAC;IACH,CAAC;IAEO,uBAAuB;QAC7B,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,oBAAoB,CAAsB,CAAC;QAC1F,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAEvF,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK;YACtC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK;YACxC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK;YACtD,iBAAiB,EAAE,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK;YACxD,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK;YAChD,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK;YAClD,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK;YACpD,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG;YACnD,SAAS,EAAE,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG;YAC3D,WAAW,EAAE,MAAM;YACnB,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,OAAO;SAC7D,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,MAAc;QAC1C,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YAC1C,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC;YACvD,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,KAAK,MAAM,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,iBAAiB;IACV,WAAW,CAAC,UAAkB;QACnC,MAAM,OAAO,GAAoD;YAC/D,MAAM,EAAE;gBACN,QAAQ,EAAE,SAAS;gBACnB,SAAS,EAAE,SAAS;gBACpB,gBAAgB,EAAE,eAAe;gBACjC,iBAAiB,EAAE,SAAS;gBAC5B,aAAa,EAAE,SAAS;gBACxB,cAAc,EAAE,SAAS;gBACzB,eAAe,EAAE,SAAS;aAC3B;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,QAAQ;gBAClB,SAAS,EAAE,SAAS;gBACpB,gBAAgB,EAAE,QAAQ;gBAC1B,iBAAiB,EAAE,SAAS;gBAC5B,aAAa,EAAE,QAAQ;gBACvB,cAAc,EAAE,SAAS;gBACzB,eAAe,EAAE,SAAS;aAC3B;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,gBAAgB;gBAC1B,SAAS,EAAE,SAAS;gBACpB,gBAAgB,EAAE,SAAS;gBAC3B,iBAAiB,EAAE,SAAS;gBAC5B,aAAa,EAAE,SAAS;gBACxB,cAAc,EAAE,SAAS;gBACzB,eAAe,EAAE,SAAS;aAC3B;YACD,OAAO,EAAE;gBACP,QAAQ,EAAE,MAAM;gBAChB,SAAS,EAAE,SAAS;gBACpB,gBAAgB,EAAE,eAAe;gBACjC,iBAAiB,EAAE,SAAS;gBAC5B,aAAa,EAAE,KAAK;gBACpB,cAAc,EAAE,SAAS;gBACzB,eAAe,EAAE,SAAS;aAC3B;YACD,QAAQ,EAAE;gBACR,QAAQ,EAAE,SAAS;gBACnB,SAAS,EAAE,SAAS;gBACpB,gBAAgB,EAAE,KAAK;gBACvB,iBAAiB,EAAE,SAAS;gBAC5B,aAAa,EAAE,KAAK;gBACpB,cAAc,EAAE,SAAS;gBACzB,eAAe,EAAE,SAAS;aAC3B;SACF,CAAC;QAEF,MAAM,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;QACnC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAC/B,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,MAAuC;QAC/D,IAAI,MAAM,CAAC,QAAQ;YAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC;QACpE,IAAI,MAAM,CAAC,SAAS;YAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC;QACvE,IAAI,MAAM,CAAC,gBAAgB;YAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC;QAC5F,IAAI,MAAM,CAAC,iBAAiB;YAAE,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,KAAK,GAAG,MAAM,CAAC,iBAAiB,CAAC;QAC/F,IAAI,MAAM,CAAC,aAAa;YAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC,aAAa,CAAC;QACnF,IAAI,MAAM,CAAC,cAAc;YAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC;QACtF,IAAI,MAAM,CAAC,eAAe;YAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,GAAG,MAAM,CAAC,eAAe,CAAC;IAC3F,CAAC;IAEM,gBAAgB;QACrB,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,oBAAoB,CAAsB,CAAC;QAC1F,OAAO,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACjF,CAAC;IAEM,qBAAqB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC7D,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAE7B,0BAA0B;QAC1B,IAAI,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC;QAE/B,oBAAoB;QACpB,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;QAC5D,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,SAAS,GAAG,oDAAoD,CAAC;QACzE,CAAC;IACH,CAAC;CACF;AA/QD,4DA+QC"}