/**
 * QR Display Component
 * Manages QR code display, downloads, and interactions
 */
import { PixTransaction } from '../../../domain/entities/pix-transaction';
import { DownloadOptions } from '../../../infrastructure/file-system/file-operations';
export interface QRDisplayElements {
    qrPlaceholder: HTMLElement;
    qrResult: HTMLElement;
    qrPreview: HTMLElement;
    brCodeText: HTMLElement;
    pixDetails: HTMLElement;
    downloadBtn: HTMLButtonElement;
    downloadSvgBtn: HTMLButtonElement;
    copyBtn: HTMLButtonElement;
    loadingSpinner: HTMLElement;
}
export interface QRDisplayCallbacks {
    onCopyBRCode: (brCode: string) => Promise<void>;
    onDownloadRequest: (format: 'png' | 'svg') => Promise<void>;
    showToast: (message: string, type: 'success' | 'error' | 'warning') => void;
}
export interface QRDisplayData {
    brCode: string;
    qrCodeElement?: HTMLElement;
    qrCodeDataUrl?: string;
    transaction: PixTransaction;
}
export declare class QRDisplayComponent {
    private elements;
    private callbacks;
    private fileService;
    private currentData;
    constructor(elements: QRDisplayElements, callbacks: QRDisplayCallbacks);
    private setupEventListeners;
    displayQRResult(data: QRDisplayData): void;
    private displayQRFromDataUrl;
    private displayPixDetails;
    private handleDownload;
    private handleCopyBRCode;
    setLoading(loading: boolean): void;
    hideResult(): void;
    getCurrentBRCode(): string | null;
    downloadCurrentQR(options: DownloadOptions): Promise<void>;
    displayStandardQR(brCode: string, transaction: PixTransaction): void;
    displayCustomizedQR(brCode: string, transaction: PixTransaction, qrElement: HTMLElement): void;
    updateMarginStyles(margin: number): void;
    getQRPreviewElement(): HTMLElement;
    showGeneratingAnimation(): void;
    showErrorState(message: string): void;
}
//# sourceMappingURL=qr-display-component.d.ts.map