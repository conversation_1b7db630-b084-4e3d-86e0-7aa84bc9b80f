/**
 * Main Application Controller
 * Orchestrates all UI components and coordinates with the application layer
 */
import { PixFormElements } from './components/pix-form-component';
import { QRCustomizationElements } from './components/qr-customization-component';
import { QRDisplayElements } from './components/qr-display-component';
import { PixFormData, QRCustomizationOptions } from '../../shared/types/pix-types';
export interface AppControllerElements extends PixFormElements, QRCustomizationElements, QRDisplayElements {
    errorModal: HTMLElement;
    errorMessage: HTMLElement;
    closeModal: HTMLButtonElement;
}
export declare class AppController {
    private elements;
    private pixFormComponent;
    private qrCustomizationComponent;
    private qrDisplayComponent;
    private generatePixQRCodeUseCase;
    private qrRepository;
    private currentFormData;
    private currentCustomization;
    constructor(elements: AppControllerElements);
    private initializeComponents;
    private setupGlobalEventListeners;
    private handleFormSubmit;
    private handleFormChange;
    private handleKeyValidation;
    private handleCustomizationChange;
    private handleToggleCustomization;
    private handleImageUpload;
    private handleMarginChange;
    private regenerateQRIfNeeded;
    private copyBRCode;
    private downloadQRCode;
    private generateFilename;
    private handleError;
    private isFormComplete;
    private setLoading;
    private showError;
    private hideModal;
    private showToast;
    applyPreset(presetName: string): void;
    reset(): void;
    getCurrentFormData(): PixFormData | null;
    getCurrentCustomization(): QRCustomizationOptions | null;
}
declare global {
    interface Window {
        pixGenerator: AppController;
        applyPreset: (presetName: string) => void;
    }
}
export declare function initializeApp(elements: AppControllerElements): AppController;
//# sourceMappingURL=app-controller.d.ts.map