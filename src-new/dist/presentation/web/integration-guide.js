"use strict";
/**
 * Integration Guide for Legacy HTML
 * Provides easy migration path from the old app.js to the new architecture
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeFromHTML = initializeFromHTML;
exports.initializeLegacyCompatibility = initializeLegacyCompatibility;
exports.checkHTMLCompatibility = checkHTMLCompatibility;
const app_controller_1 = require("./app-controller");
/**
 * Automatically initializes the new architecture with existing HTML elements
 * This provides a drop-in replacement for the old PixQRGeneratorIntegrated class
 */
function initializeFromHTML() {
    // Get all required elements from the existing HTML
    const elements = {
        // Form elements
        form: document.getElementById('pixForm'),
        keyTypeSelect: document.getElementById('keyType'),
        pixKeyInput: document.getElementById('pixKey'),
        receiverNameInput: document.getElementById('receiverName'),
        receiverCityInput: document.getElementById('receiverCity'),
        amountInput: document.getElementById('amount'),
        referenceInput: document.getElementById('reference'),
        descriptionInput: document.getElementById('description'),
        generateBtn: document.getElementById('generateBtn'),
        keyValidation: document.getElementById('keyValidation'),
        // Customization elements
        customizationToggle: document.getElementById('customizationToggle'),
        customizationPanel: document.getElementById('customizationPanel'),
        dotsType: document.getElementById('dots-type'),
        dotsColor: document.getElementById('dots-color'),
        cornerSquareType: document.getElementById('corner-square-type'),
        cornerSquareColor: document.getElementById('corner-square-color'),
        cornerDotType: document.getElementById('corner-dot-type'),
        cornerDotColor: document.getElementById('corner-dot-color'),
        backgroundColor: document.getElementById('background-color'),
        qrSize: document.getElementById('qr-size'),
        imageSize: document.getElementById('image-size'),
        imageSizeValue: document.getElementById('image-size-value'),
        imageMargin: document.getElementById('image-margin'),
        hideBackgroundDots: document.getElementById('hide-background-dots'),
        centerImage: document.getElementById('center-image'),
        marginButtons: document.querySelectorAll('.margin-btn'),
        // Display elements
        qrPlaceholder: document.getElementById('qrPlaceholder'),
        qrResult: document.getElementById('qrResult'),
        qrPreview: document.getElementById('qr-preview'),
        brCodeText: document.getElementById('brCodeText'),
        pixDetails: document.getElementById('pixDetails'),
        downloadBtn: document.getElementById('downloadBtn'),
        downloadSvgBtn: document.getElementById('downloadSvgBtn'),
        copyBtn: document.getElementById('copyBtn'),
        loadingSpinner: document.getElementById('loadingSpinner'),
        // Modal elements
        errorModal: document.getElementById('errorModal'),
        errorMessage: document.getElementById('errorMessage'),
        closeModal: document.getElementById('closeModal')
    };
    // Validate all required elements exist
    validateElements(elements);
    // Initialize and return the app controller
    return new app_controller_1.AppController(elements);
}
/**
 * Validates that all required HTML elements exist
 */
function validateElements(elements) {
    const missingElements = [];
    Object.entries(elements).forEach(([key, element]) => {
        if (!element) {
            missingElements.push(key);
        }
    });
    if (missingElements.length > 0) {
        throw new Error(`Missing required HTML elements: ${missingElements.join(', ')}. ` +
            'Make sure your HTML contains all required elements with correct IDs.');
    }
}
/**
 * Legacy compatibility function
 * Maintains the same global interface as the original app.js
 */
function initializeLegacyCompatibility() {
    document.addEventListener('DOMContentLoaded', () => {
        try {
            const appController = initializeFromHTML();
            // Maintain backward compatibility
            window.pixGenerator = appController;
            window.applyPreset = (presetName) => {
                appController.applyPreset(presetName);
            };
            console.log('✅ PIX QR Generator initialized successfully with Clean Architecture');
        }
        catch (error) {
            console.error('❌ Failed to initialize PIX QR Generator:', error);
            // Fallback error message for users
            const errorDiv = document.createElement('div');
            errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 15px;
        border-radius: 8px;
        z-index: 10000;
        max-width: 300px;
      `;
            errorDiv.innerHTML = `
        <strong>Erro de Inicialização</strong><br>
        Verifique se todos os elementos HTML necessários estão presentes.
      `;
            document.body.appendChild(errorDiv);
            setTimeout(() => {
                document.body.removeChild(errorDiv);
            }, 5000);
        }
    });
}
/**
 * Development helper to check element availability
 */
function checkHTMLCompatibility() {
    const requiredIds = [
        'pixForm', 'keyType', 'pixKey', 'receiverName', 'receiverCity',
        'amount', 'reference', 'description', 'generateBtn', 'keyValidation',
        'customizationToggle', 'customizationPanel', 'dots-type', 'dots-color',
        'corner-square-type', 'corner-square-color', 'corner-dot-type',
        'corner-dot-color', 'background-color', 'qr-size', 'image-size',
        'image-size-value', 'image-margin', 'hide-background-dots',
        'center-image', 'qrPlaceholder', 'qrResult', 'qr-preview',
        'brCodeText', 'pixDetails', 'downloadBtn', 'downloadSvgBtn',
        'copyBtn', 'loadingSpinner', 'errorModal', 'errorMessage', 'closeModal'
    ];
    const missing = requiredIds.filter(id => !document.getElementById(id));
    const suggestions = missing.map(id => `Add: <element id="${id}">...</element>`);
    return {
        compatible: missing.length === 0,
        missing,
        suggestions
    };
}
// Auto-initialize when this module is imported (for easy migration)
if (typeof window !== 'undefined') {
    initializeLegacyCompatibility();
}
//# sourceMappingURL=integration-guide.js.map