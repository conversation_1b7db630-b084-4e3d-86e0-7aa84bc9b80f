"use strict";
/**
 * PIX Controller
 * Handles HTTP requests for PIX QR code generation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PixController = void 0;
const pix_key_1 = require("../../domain/value-objects/pix-key");
const money_1 = require("../../domain/value-objects/money");
const pix_transaction_1 = require("../../domain/entities/pix-transaction");
const pix_errors_1 = require("../../shared/errors/pix-errors");
class PixController {
    constructor(generatePixQRCodeUseCase) {
        this.generatePixQRCodeUseCase = generatePixQRCodeUseCase;
    }
    async generateQRCode(request) {
        try {
            // Validate and create domain objects
            const pixKey = pix_key_1.PixKey.create(request.pixKey, request.keyType);
            const amount = money_1.Money.create(request.amount || 0);
            const transactionParams = {
                pixKey,
                receiverName: request.receiverName,
                receiverCity: request.receiverCity,
                amount
            };
            if (request.reference) {
                transactionParams.reference = request.reference;
            }
            if (request.description) {
                transactionParams.description = request.description;
            }
            const transaction = pix_transaction_1.PixTransaction.create(transactionParams);
            // Execute use case
            const useCaseRequest = {
                transaction,
                isDynamic: false
            };
            if (request.customization) {
                useCaseRequest.customization = request.customization;
            }
            const result = await this.generatePixQRCodeUseCase.execute(useCaseRequest);
            const responseData = {
                brCode: result.brCode,
                transactionDetails: result.transaction.getDisplayDetails()
            };
            if (result.qrCodeDataUrl) {
                responseData.qrCodeDataUrl = result.qrCodeDataUrl;
            }
            return {
                success: true,
                data: responseData
            };
        }
        catch (error) {
            return this.handleError(error);
        }
    }
    async validatePixKey(pixKey, keyType) {
        try {
            pix_key_1.PixKey.create(pixKey, keyType);
            return {
                isValid: true,
                message: this.getSuccessMessage(keyType)
            };
        }
        catch (error) {
            if (error instanceof pix_errors_1.ValidationError) {
                return {
                    isValid: false,
                    message: this.getErrorMessage(keyType)
                };
            }
            return {
                isValid: false,
                message: 'Erro interno de validação'
            };
        }
    }
    handleError(error) {
        if (error instanceof pix_errors_1.ValidationError) {
            const errorResponse = {
                message: error.message,
                code: 'VALIDATION_ERROR'
            };
            const field = this.extractFieldFromError(error.message);
            if (field) {
                errorResponse.field = field;
            }
            return {
                success: false,
                error: errorResponse
            };
        }
        if (error instanceof pix_errors_1.PixError) {
            return {
                success: false,
                error: {
                    message: error.message,
                    code: error.name
                }
            };
        }
        // Generic error handling
        return {
            success: false,
            error: {
                message: 'Erro interno do servidor',
                code: 'INTERNAL_ERROR'
            }
        };
    }
    extractFieldFromError(message) {
        const fieldMatch = message.match(/field "([^"]+)"/);
        return fieldMatch ? fieldMatch[1] : undefined;
    }
    getSuccessMessage(keyType) {
        const messages = {
            cpf: 'CPF válido',
            phone: 'Telefone válido',
            email: 'Email válido',
            random: 'Chave válida'
        };
        return messages[keyType];
    }
    getErrorMessage(keyType) {
        const messages = {
            cpf: 'CPF inválido',
            phone: 'Telefone inválido',
            email: 'Email inválido',
            random: 'Chave deve ter pelo menos 10 caracteres'
        };
        return messages[keyType];
    }
}
exports.PixController = PixController;
//# sourceMappingURL=pix-controller.js.map