/**
 * PIX Controller
 * Handles HTTP requests for PIX QR code generation
 */
import { GeneratePixQRCodeUseCase } from '../../application/usecases/generate-pix-qrcode';
import { PixKeyType } from '../../shared/types/pix-types';
export interface PixControllerRequest {
    keyType: PixKeyType;
    pixKey: string;
    receiverName: string;
    receiverCity: string;
    amount?: number;
    reference?: string;
    description?: string;
    customization?: {
        dotsType: string;
        dotsColor: string;
        cornerSquareType: string;
        cornerSquareColor: string;
        cornerDotType: string;
        cornerDotColor: string;
        backgroundColor: string;
        qrSize: number;
        imageSize: number;
        imageMargin: number;
        hideBackgroundDots: boolean;
    };
}
export interface PixControllerResponse {
    success: boolean;
    data?: {
        brCode: string;
        qrCodeDataUrl?: string;
        transactionDetails: Array<{
            label: string;
            value: string;
        }>;
    };
    error?: {
        message: string;
        code: string;
        field?: string;
    };
}
export declare class PixController {
    private generatePixQRCodeUseCase;
    constructor(generatePixQRCodeUseCase: GeneratePixQRCodeUseCase);
    generateQRCode(request: PixControllerRequest): Promise<PixControllerResponse>;
    validatePixKey(pixKey: string, keyType: PixKeyType): Promise<{
        isValid: boolean;
        message: string;
    }>;
    private handleError;
    private extractFieldFromError;
    private getSuccessMessage;
    private getErrorMessage;
}
//# sourceMappingURL=pix-controller.d.ts.map