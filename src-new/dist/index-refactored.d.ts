/**
 * Complete refactored library entry point
 * Exports all layers following Clean Architecture
 */
export { <PERSON>x<PERSON><PERSON> } from './domain/value-objects/pix-key';
export { Money } from './domain/value-objects/money';
export { PixTransaction } from './domain/entities/pix-transaction';
export { BRCodeGenerator } from './domain/services/br-code-generator';
import { GeneratePixQRCodeUseCase } from './application/usecases/generate-pix-qrcode';
export { GeneratePixQRCodeUseCase, type GeneratePixQRCodeRequest, type GeneratePixQRCodeResponse, type QRCodeRepository } from './application/usecases/generate-pix-qrcode';
export { QRCodeStylingProvider } from './infrastructure/qr-generators/qr-code-styling-provider';
export { ExternalAPIProvider } from './infrastructure/qr-generators/external-api-provider';
export { QRGeneratorFactory } from './infrastructure/qr-generators/qr-generator-factory';
export { FileOperationsService } from './infrastructure/file-system/file-operations';
export { QRCodeRepositoryEnhanced, createEnhancedQRCodeRepository, createQRCodeRepositoryWithStyling, createQRCodeRepositoryWithExternalAPI } from './infrastructure/repositories/qr-code-repository-enhanced';
import { PixController } from './presentation/controllers/pix-controller';
export { PixController } from './presentation/controllers/pix-controller';
export { PixFormComponent } from './presentation/web/components/pix-form-component';
export { QRCustomizationComponent } from './presentation/web/components/qr-customization-component';
export { QRDisplayComponent } from './presentation/web/components/qr-display-component';
export { AppController } from './presentation/web/app-controller';
import { checkHTMLCompatibility } from './presentation/web/integration-guide';
export { initializeFromHTML, initializeLegacyCompatibility, checkHTMLCompatibility } from './presentation/web/integration-guide';
export * from './shared/types/pix-types';
export * from './shared/errors/pix-errors';
/**
 * Complete application setup with all dependencies
 */
export declare function createPixQRApplication(config?: {
    preferredQRProvider?: 'qr-code-styling' | 'external-api' | 'auto';
    fallbackEnabled?: boolean;
}): {
    repository: import("./infrastructure/repositories/qr-code-repository-enhanced").QRCodeRepositoryEnhanced;
    useCase: GeneratePixQRCodeUseCase;
    controller: PixController;
    generatePixQR(data: {
        keyType: "cpf" | "phone" | "email" | "random";
        pixKey: string;
        receiverName: string;
        receiverCity: string;
        amount?: number;
        reference?: string;
        description?: string;
    }): Promise<import("./presentation/controllers/pix-controller").PixControllerResponse>;
    validatePixKey(pixKey: string, keyType: "cpf" | "phone" | "email" | "random"): Promise<{
        isValid: boolean;
        message: string;
    }>;
};
/**
 * Quick setup for web applications (drop-in replacement)
 */
export declare function initializeWebApp(): import("./presentation/web/app-controller").AppController;
/**
 * Server-side setup (Node.js compatible)
 */
export declare function createServerSideApp(): {
    repository: import("./infrastructure/repositories/qr-code-repository-enhanced").QRCodeRepositoryEnhanced;
    useCase: GeneratePixQRCodeUseCase;
    controller: PixController;
    generatePixQR(data: {
        keyType: "cpf" | "phone" | "email" | "random";
        pixKey: string;
        receiverName: string;
        receiverCity: string;
        amount?: number;
        reference?: string;
        description?: string;
    }): Promise<import("./presentation/controllers/pix-controller").PixControllerResponse>;
    validatePixKey(pixKey: string, keyType: "cpf" | "phone" | "email" | "random"): Promise<{
        isValid: boolean;
        message: string;
    }>;
};
export declare const version: {
    version: string;
    architecture: string;
    typescript: boolean;
    features: string[];
};
export declare const dev: {
    checkCompatibility: typeof checkHTMLCompatibility;
    getDependencyTree: () => {
        domain: string[];
        application: string[];
        infrastructure: string[];
        presentation: string[];
        shared: string[];
    };
    validateArchitecture: () => {
        isValid: boolean;
        violations: string[];
    };
};
declare const _default: {
    createPixQRApplication: typeof createPixQRApplication;
    initializeWebApp: typeof initializeWebApp;
    createServerSideApp: typeof createServerSideApp;
    version: {
        version: string;
        architecture: string;
        typescript: boolean;
        features: string[];
    };
    dev: {
        checkCompatibility: typeof checkHTMLCompatibility;
        getDependencyTree: () => {
            domain: string[];
            application: string[];
            infrastructure: string[];
            presentation: string[];
            shared: string[];
        };
        validateArchitecture: () => {
            isValid: boolean;
            violations: string[];
        };
    };
};
export default _default;
//# sourceMappingURL=index-refactored.d.ts.map