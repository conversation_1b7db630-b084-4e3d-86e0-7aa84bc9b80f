"use strict";
/**
 * Complete refactored library entry point
 * Exports all layers following Clean Architecture
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dev = exports.version = exports.checkHTMLCompatibility = exports.initializeLegacyCompatibility = exports.initializeFromHTML = exports.AppController = exports.QRDisplayComponent = exports.QRCustomizationComponent = exports.PixFormComponent = exports.PixController = exports.createQRCodeRepositoryWithExternalAPI = exports.createQRCodeRepositoryWithStyling = exports.createEnhancedQRCodeRepository = exports.QRCodeRepositoryEnhanced = exports.FileOperationsService = exports.QRGeneratorFactory = exports.ExternalAPIProvider = exports.QRCodeStylingProvider = exports.GeneratePixQRCodeUseCase = exports.BRCodeGenerator = exports.PixTransaction = exports.Money = exports.PixKey = void 0;
exports.createPixQRApplication = createPixQRApplication;
exports.initializeWebApp = initializeWebApp;
exports.createServerSideApp = createServerSideApp;
// === DOMAIN LAYER ===
var pix_key_1 = require("./domain/value-objects/pix-key");
Object.defineProperty(exports, "PixKey", { enumerable: true, get: function () { return pix_key_1.PixKey; } });
var money_1 = require("./domain/value-objects/money");
Object.defineProperty(exports, "Money", { enumerable: true, get: function () { return money_1.Money; } });
var pix_transaction_1 = require("./domain/entities/pix-transaction");
Object.defineProperty(exports, "PixTransaction", { enumerable: true, get: function () { return pix_transaction_1.PixTransaction; } });
var br_code_generator_1 = require("./domain/services/br-code-generator");
Object.defineProperty(exports, "BRCodeGenerator", { enumerable: true, get: function () { return br_code_generator_1.BRCodeGenerator; } });
// === APPLICATION LAYER ===
const generate_pix_qrcode_1 = require("./application/usecases/generate-pix-qrcode");
var generate_pix_qrcode_2 = require("./application/usecases/generate-pix-qrcode");
Object.defineProperty(exports, "GeneratePixQRCodeUseCase", { enumerable: true, get: function () { return generate_pix_qrcode_2.GeneratePixQRCodeUseCase; } });
// === INFRASTRUCTURE LAYER ===
// QR Generators
var qr_code_styling_provider_1 = require("./infrastructure/qr-generators/qr-code-styling-provider");
Object.defineProperty(exports, "QRCodeStylingProvider", { enumerable: true, get: function () { return qr_code_styling_provider_1.QRCodeStylingProvider; } });
var external_api_provider_1 = require("./infrastructure/qr-generators/external-api-provider");
Object.defineProperty(exports, "ExternalAPIProvider", { enumerable: true, get: function () { return external_api_provider_1.ExternalAPIProvider; } });
var qr_generator_factory_1 = require("./infrastructure/qr-generators/qr-generator-factory");
Object.defineProperty(exports, "QRGeneratorFactory", { enumerable: true, get: function () { return qr_generator_factory_1.QRGeneratorFactory; } });
// File System
var file_operations_1 = require("./infrastructure/file-system/file-operations");
Object.defineProperty(exports, "FileOperationsService", { enumerable: true, get: function () { return file_operations_1.FileOperationsService; } });
// Repositories
const qr_code_repository_enhanced_1 = require("./infrastructure/repositories/qr-code-repository-enhanced");
var qr_code_repository_enhanced_2 = require("./infrastructure/repositories/qr-code-repository-enhanced");
Object.defineProperty(exports, "QRCodeRepositoryEnhanced", { enumerable: true, get: function () { return qr_code_repository_enhanced_2.QRCodeRepositoryEnhanced; } });
Object.defineProperty(exports, "createEnhancedQRCodeRepository", { enumerable: true, get: function () { return qr_code_repository_enhanced_2.createEnhancedQRCodeRepository; } });
Object.defineProperty(exports, "createQRCodeRepositoryWithStyling", { enumerable: true, get: function () { return qr_code_repository_enhanced_2.createQRCodeRepositoryWithStyling; } });
Object.defineProperty(exports, "createQRCodeRepositoryWithExternalAPI", { enumerable: true, get: function () { return qr_code_repository_enhanced_2.createQRCodeRepositoryWithExternalAPI; } });
// === PRESENTATION LAYER ===
// Controllers
const pix_controller_1 = require("./presentation/controllers/pix-controller");
var pix_controller_2 = require("./presentation/controllers/pix-controller");
Object.defineProperty(exports, "PixController", { enumerable: true, get: function () { return pix_controller_2.PixController; } });
// Web Components
var pix_form_component_1 = require("./presentation/web/components/pix-form-component");
Object.defineProperty(exports, "PixFormComponent", { enumerable: true, get: function () { return pix_form_component_1.PixFormComponent; } });
var qr_customization_component_1 = require("./presentation/web/components/qr-customization-component");
Object.defineProperty(exports, "QRCustomizationComponent", { enumerable: true, get: function () { return qr_customization_component_1.QRCustomizationComponent; } });
var qr_display_component_1 = require("./presentation/web/components/qr-display-component");
Object.defineProperty(exports, "QRDisplayComponent", { enumerable: true, get: function () { return qr_display_component_1.QRDisplayComponent; } });
// Main App Controller
var app_controller_1 = require("./presentation/web/app-controller");
Object.defineProperty(exports, "AppController", { enumerable: true, get: function () { return app_controller_1.AppController; } });
// Integration helpers
const integration_guide_1 = require("./presentation/web/integration-guide");
var integration_guide_2 = require("./presentation/web/integration-guide");
Object.defineProperty(exports, "initializeFromHTML", { enumerable: true, get: function () { return integration_guide_2.initializeFromHTML; } });
Object.defineProperty(exports, "initializeLegacyCompatibility", { enumerable: true, get: function () { return integration_guide_2.initializeLegacyCompatibility; } });
Object.defineProperty(exports, "checkHTMLCompatibility", { enumerable: true, get: function () { return integration_guide_2.checkHTMLCompatibility; } });
// === SHARED LAYER ===
__exportStar(require("./shared/types/pix-types"), exports);
__exportStar(require("./shared/errors/pix-errors"), exports);
// === FACTORY FUNCTIONS ===
/**
 * Complete application setup with all dependencies
 */
function createPixQRApplication(config) {
    let repositoryConfig;
    if (config) {
        repositoryConfig = {};
        if (config.preferredQRProvider) {
            repositoryConfig.preferredProvider = config.preferredQRProvider;
        }
        if (config.fallbackEnabled !== undefined) {
            repositoryConfig.fallbackEnabled = config.fallbackEnabled;
        }
    }
    const repository = (0, qr_code_repository_enhanced_1.createEnhancedQRCodeRepository)(repositoryConfig);
    const useCase = new generate_pix_qrcode_1.GeneratePixQRCodeUseCase(repository);
    const controller = new pix_controller_1.PixController(useCase);
    return {
        repository,
        useCase,
        controller,
        // Convenience methods
        async generatePixQR(data) {
            return await controller.generateQRCode(data);
        },
        async validatePixKey(pixKey, keyType) {
            return await controller.validatePixKey(pixKey, keyType);
        }
    };
}
/**
 * Quick setup for web applications (drop-in replacement)
 */
function initializeWebApp() {
    return (0, integration_guide_1.initializeFromHTML)();
}
/**
 * Server-side setup (Node.js compatible)
 */
function createServerSideApp() {
    return createPixQRApplication({
        preferredQRProvider: 'external-api',
        fallbackEnabled: false
    });
}
// === VERSION INFO ===
exports.version = {
    version: '2.0.0',
    architecture: 'Clean Architecture',
    typescript: true,
    features: [
        'Domain-driven design',
        'Type-safe error handling',
        'Multiple QR providers',
        'Zero-margin QR generation',
        'Advanced customization',
        'File operations',
        'Web components',
        'Legacy compatibility'
    ]
};
// === DEVELOPMENT HELPERS ===
exports.dev = {
    checkCompatibility: integration_guide_1.checkHTMLCompatibility,
    // Debug information
    getDependencyTree: () => ({
        domain: ['PixKey', 'Money', 'PixTransaction', 'BRCodeGenerator'],
        application: ['GeneratePixQRCodeUseCase'],
        infrastructure: ['QRGeneratorFactory', 'FileOperationsService', 'QRCodeRepositoryEnhanced'],
        presentation: ['AppController', 'PixFormComponent', 'QRCustomizationComponent', 'QRDisplayComponent'],
        shared: ['Types', 'Errors', 'Constants']
    }),
    // Architecture validation
    validateArchitecture: () => {
        const violations = [];
        // Check that domain doesn't import from other layers
        // This would be implemented with static analysis in a real project
        return {
            isValid: violations.length === 0,
            violations
        };
    }
};
// Default export for easy importing
exports.default = {
    createPixQRApplication,
    initializeWebApp,
    createServerSideApp,
    version: exports.version,
    dev: exports.dev
};
//# sourceMappingURL=index-refactored.js.map