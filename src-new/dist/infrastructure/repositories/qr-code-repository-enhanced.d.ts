/**
 * Enhanced QR Code Repository Implementation
 * Uses the new infrastructure layer with factory pattern
 */
import { QRCodeRepository } from '../../application/usecases/generate-pix-qrcode';
import { QRGenerationOptions } from '../../shared/types/pix-types';
import { QRGeneratorConfig } from '../qr-generators/qr-generator-factory';
import { DownloadOptions } from '../file-system/file-operations';
export interface EnhancedQRCodeRepository extends QRCodeRepository {
    downloadQRCode(dataUrl: string, options: DownloadOptions): Promise<void>;
    generateQRCodeWithImage(options: QRGenerationOptions, imageFile: File): Promise<string>;
    generateStandardQRCode(data: string): Promise<string>;
}
export declare class QRCodeRepositoryEnhanced implements EnhancedQRCodeRepository {
    private qrGenerator;
    private fileService;
    constructor(config?: QRGeneratorConfig);
    generateQRCode(options: QRGenerationOptions): Promise<string>;
    generateZeroMarginQRCode(options: QRGenerationOptions): Promise<string>;
    generateStandardQRCode(data: string): Promise<string>;
    generateQRCodeWithImage(options: QRGenerationOptions, imageFile: File): Promise<string>;
    downloadQRCode(dataUrl: string, options: DownloadOptions): Promise<void>;
    private extractDataUrl;
    getQRCodeAsBlob(options: QRGenerationOptions, format?: string): Promise<Blob>;
    getQRCodeElement(options: QRGenerationOptions): Promise<HTMLElement>;
    getProviderInfo(): {
        current: string;
        available: string[];
    };
}
export declare function createEnhancedQRCodeRepository(config?: QRGeneratorConfig): QRCodeRepositoryEnhanced;
export declare function createQRCodeRepositoryWithStyling(): QRCodeRepositoryEnhanced;
export declare function createQRCodeRepositoryWithExternalAPI(): QRCodeRepositoryEnhanced;
//# sourceMappingURL=qr-code-repository-enhanced.d.ts.map