"use strict";
/**
 * QR Code Repository Implementation
 * Implements QR code generation using external libraries
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.QRCodeRepositoryImpl = void 0;
exports.createQRCodeRepository = createQRCodeRepository;
const pix_errors_1 = require("../../shared/errors/pix-errors");
class QRCodeRepositoryImpl {
    async generateQRCode(options) {
        try {
            // Always try to use local qr-code-styling library first for compatibility
            if (typeof window !== 'undefined' && window.QRCodeStyling) {
                return this.generateWithQRCodeStyling(options);
            }
            // Only fallback to external API if local library is not available
            console.warn('QRCodeStyling library not found, falling back to external API');
            return this.generateWithExternalAPI(options);
        }
        catch (error) {
            throw new pix_errors_1.QRGenerationError(`Failed to generate QR code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateZeroMarginQRCode(options) {
        try {
            // Special handling for zero margin QR codes
            const modifiedOptions = {
                ...options,
                margin: 0
            };
            return this.generateWithAdvancedProcessing(modifiedOptions);
        }
        catch (error) {
            throw new pix_errors_1.QRGenerationError(`Failed to generate zero margin QR code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateWithQRCodeStyling(options) {
        return new Promise((resolve, reject) => {
            try {
                const qrCode = new window.QRCodeStyling(options);
                // Create temporary container
                const container = document.createElement('div');
                container.style.position = 'absolute';
                container.style.left = '-9999px';
                document.body.appendChild(container);
                qrCode.append(container);
                setTimeout(() => {
                    const canvas = container.querySelector('canvas');
                    if (canvas) {
                        const dataUrl = canvas.toDataURL();
                        document.body.removeChild(container);
                        resolve(dataUrl);
                    }
                    else {
                        document.body.removeChild(container);
                        reject(new Error('Failed to generate canvas'));
                    }
                }, 100);
            }
            catch (error) {
                reject(error);
            }
        });
    }
    async generateWithExternalAPI(options) {
        const apiUrl = `https://api.qrserver.com/v1/create-qr-code/`;
        const params = new URLSearchParams({
            size: `${options.width}x${options.height}`,
            data: options.data,
            ecc: options.qrOptions.errorCorrectionLevel,
            format: 'png'
        });
        const response = await fetch(`${apiUrl}?${params.toString()}`);
        if (!response.ok) {
            throw new Error(`External API request failed: ${response.statusText}`);
        }
        const blob = await response.blob();
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result);
            reader.readAsDataURL(blob);
        });
    }
    async generateWithAdvancedProcessing(options) {
        // This would implement the advanced zero-margin processing 
        // that exists in the original app.js file
        // For now, fallback to standard generation
        return this.generateWithQRCodeStyling(options);
    }
}
exports.QRCodeRepositoryImpl = QRCodeRepositoryImpl;
// Factory function for dependency injection
function createQRCodeRepository() {
    return new QRCodeRepositoryImpl();
}
//# sourceMappingURL=qr-code-repository-impl.js.map