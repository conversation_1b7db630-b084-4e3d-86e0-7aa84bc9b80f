/**
 * QR Code Repository Implementation
 * Implements QR code generation using external libraries
 */
import { QRCodeRepository } from '../../application/usecases/generate-pix-qrcode';
import { QRGenerationOptions } from '../../shared/types/pix-types';
export declare class QRCodeRepositoryImpl implements QRCodeRepository {
    generateQRCode(options: QRGenerationOptions): Promise<string>;
    generateZeroMarginQRCode(options: QRGenerationOptions): Promise<string>;
    private generateWithQRCodeStyling;
    private generateWithExternalAPI;
    private generateWithAdvancedProcessing;
}
export declare function createQRCodeRepository(): QRCodeRepository;
//# sourceMappingURL=qr-code-repository-impl.d.ts.map