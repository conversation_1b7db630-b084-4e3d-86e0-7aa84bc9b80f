"use strict";
/**
 * Enhanced QR Code Repository Implementation
 * Uses the new infrastructure layer with factory pattern
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.QRCodeRepositoryEnhanced = void 0;
exports.createEnhancedQRCodeRepository = createEnhancedQRCodeRepository;
exports.createQRCodeRepositoryWithStyling = createQRCodeRepositoryWithStyling;
exports.createQRCodeRepositoryWithExternalAPI = createQRCodeRepositoryWithExternalAPI;
const pix_errors_1 = require("../../shared/errors/pix-errors");
const qr_generator_factory_1 = require("../qr-generators/qr-generator-factory");
const file_operations_1 = require("../file-system/file-operations");
class QRCodeRepositoryEnhanced {
    constructor(config) {
        this.qrGenerator = new qr_generator_factory_1.QRGeneratorFactory(config);
        this.fileService = new file_operations_1.FileOperationsService();
    }
    async generateQRCode(options) {
        try {
            const result = await this.qrGenerator.generateQRCode(options);
            if (!result.dataUrl) {
                // If no dataUrl from provider, extract it from element
                result.dataUrl = await this.extractDataUrl(result.element);
            }
            return result.dataUrl;
        }
        catch (error) {
            throw new pix_errors_1.QRGenerationError(`Enhanced QR generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateZeroMarginQRCode(options) {
        try {
            const result = await this.qrGenerator.generateZeroMarginQRCode(options);
            if (!result.dataUrl) {
                result.dataUrl = await this.extractDataUrl(result.element);
            }
            return result.dataUrl;
        }
        catch (error) {
            throw new pix_errors_1.QRGenerationError(`Zero margin QR generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateStandardQRCode(data) {
        try {
            const result = await this.qrGenerator.generateStandardQRCode(data);
            if (!result.dataUrl) {
                result.dataUrl = await this.extractDataUrl(result.element);
            }
            return result.dataUrl;
        }
        catch (error) {
            throw new pix_errors_1.QRGenerationError(`Standard QR generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateQRCodeWithImage(options, imageFile) {
        try {
            // Validate image file first
            const validation = this.fileService.validateImageFile(imageFile);
            if (!validation.isValid) {
                throw new Error(validation.error || 'Invalid image file');
            }
            const result = await this.qrGenerator.generateWithCustomImage(options, imageFile);
            if (!result.dataUrl) {
                result.dataUrl = await this.extractDataUrl(result.element);
            }
            return result.dataUrl;
        }
        catch (error) {
            throw new pix_errors_1.QRGenerationError(`QR generation with image failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async downloadQRCode(dataUrl, options) {
        try {
            await this.fileService.downloadFile(dataUrl, options);
        }
        catch (error) {
            throw new pix_errors_1.QRGenerationError(`QR code download failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async extractDataUrl(element) {
        if (element.tagName.toLowerCase() === 'canvas') {
            const canvas = element;
            return canvas.toDataURL('image/png');
        }
        if (element.tagName.toLowerCase() === 'svg') {
            const svg = element;
            const svgRect = svg.getBoundingClientRect();
            return await this.fileService.svgToPng(svg, svgRect.width, svgRect.height);
        }
        if (element.tagName.toLowerCase() === 'img') {
            const img = element;
            return img.src;
        }
        throw new Error('Cannot extract data URL from element type: ' + element.tagName);
    }
    // Additional utility methods
    async getQRCodeAsBlob(options, format = 'image/png') {
        const dataUrl = await this.generateQRCode(options);
        const response = await fetch(dataUrl);
        return await response.blob();
    }
    async getQRCodeElement(options) {
        const result = await this.qrGenerator.generateQRCode(options);
        return result.element;
    }
    getProviderInfo() {
        // This would be implemented to return info about available providers
        return {
            current: 'auto-selected',
            available: ['qr-code-styling', 'external-api']
        };
    }
}
exports.QRCodeRepositoryEnhanced = QRCodeRepositoryEnhanced;
// Factory functions for different configurations
function createEnhancedQRCodeRepository(config) {
    return new QRCodeRepositoryEnhanced(config);
}
function createQRCodeRepositoryWithStyling() {
    return new QRCodeRepositoryEnhanced({
        preferredProvider: 'qr-code-styling',
        fallbackEnabled: true
    });
}
function createQRCodeRepositoryWithExternalAPI() {
    return new QRCodeRepositoryEnhanced({
        preferredProvider: 'external-api',
        fallbackEnabled: false
    });
}
//# sourceMappingURL=qr-code-repository-enhanced.js.map