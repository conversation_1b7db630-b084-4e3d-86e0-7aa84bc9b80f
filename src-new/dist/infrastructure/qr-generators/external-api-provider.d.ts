/**
 * External API QR Code Provider
 * Fallback implementation using external QR code generation APIs
 */
import { QRGenerationOptions } from '../../shared/types/pix-types';
export interface ExternalAPIConfig {
    baseUrl: string;
    timeout: number;
    retries: number;
}
export interface ExternalQRResult {
    dataUrl: string;
    element: HTMLImageElement;
}
export declare class ExternalAPIProvider {
    private config;
    constructor(config?: Partial<ExternalAPIConfig>);
    generateQRCode(options: QRGenerationOptions): Promise<ExternalQRResult>;
    generateSimpleQRCode(data: string, size?: {
        width: number;
        height: number;
    }): Promise<ExternalQRResult>;
    private buildApiUrl;
    private fetchWithTimeout;
    private blobToDataUrl;
    private createImageElement;
    private delay;
    static createQRServerProvider(): ExternalAPIProvider;
    static createQuickChartProvider(): ExternalAPIProvider;
}
//# sourceMappingURL=external-api-provider.d.ts.map