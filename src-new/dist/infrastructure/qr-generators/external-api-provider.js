"use strict";
/**
 * External API QR Code Provider
 * Fallback implementation using external QR code generation APIs
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExternalAPIProvider = void 0;
const pix_errors_1 = require("../../shared/errors/pix-errors");
class ExternalAPIProvider {
    constructor(config) {
        this.config = {
            baseUrl: 'https://api.qrserver.com/v1/create-qr-code/',
            timeout: 10000,
            retries: 3,
            ...config
        };
    }
    async generateQRCode(options) {
        const url = this.buildApiUrl(options);
        for (let attempt = 1; attempt <= this.config.retries; attempt++) {
            try {
                const response = await this.fetchWithTimeout(url, this.config.timeout);
                if (!response.ok) {
                    throw new Error(`API request failed: ${response.status} ${response.statusText}`);
                }
                const blob = await response.blob();
                const dataUrl = await this.blobToDataUrl(blob);
                const element = await this.createImageElement(dataUrl);
                return { dataUrl, element };
            }
            catch (error) {
                if (attempt === this.config.retries) {
                    throw new pix_errors_1.QRGenerationError(`External API generation failed after ${this.config.retries} attempts: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
                // Wait before retry with exponential backoff
                await this.delay(Math.pow(2, attempt - 1) * 1000);
            }
        }
        throw new pix_errors_1.QRGenerationError('All retry attempts exhausted');
    }
    async generateSimpleQRCode(data, size = { width: 300, height: 300 }) {
        const simpleOptions = {
            width: size.width,
            height: size.height,
            type: 'canvas',
            data,
            margin: 0,
            qrOptions: {
                typeNumber: 0,
                errorCorrectionLevel: 'M'
            },
            dotsOptions: {
                color: '#000000',
                type: 'square',
                roundSize: false
            },
            backgroundOptions: {
                color: '#FFFFFF',
                round: 0
            },
            cornersSquareOptions: {
                color: '#000000'
            },
            cornersDotOptions: {
                color: '#000000'
            }
        };
        return this.generateQRCode(simpleOptions);
    }
    buildApiUrl(options) {
        const params = new URLSearchParams({
            size: `${options.width}x${options.height}`,
            data: options.data,
            ecc: options.qrOptions.errorCorrectionLevel,
            format: 'png',
            margin: options.margin.toString(),
            color: options.dotsOptions.color.replace('#', ''),
            bgcolor: options.backgroundOptions.color.replace('#', '')
        });
        return `${this.config.baseUrl}?${params.toString()}`;
    }
    async fetchWithTimeout(url, timeout) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        try {
            const response = await fetch(url, {
                signal: controller.signal,
                headers: {
                    'Accept': 'image/png',
                    'User-Agent': 'QR-Code-Styling-Clean/2.0'
                }
            });
            clearTimeout(timeoutId);
            return response;
        }
        catch (error) {
            clearTimeout(timeoutId);
            if (error instanceof Error && error.name === 'AbortError') {
                throw new Error(`Request timeout after ${timeout}ms`);
            }
            throw error;
        }
    }
    async blobToDataUrl(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result);
            reader.onerror = () => reject(new Error('Failed to convert blob to data URL'));
            reader.readAsDataURL(blob);
        });
    }
    async createImageElement(dataUrl) {
        return new Promise((resolve, reject) => {
            const img = document.createElement('img');
            img.style.maxWidth = '100%';
            img.style.borderRadius = '8px';
            img.alt = 'QR Code PIX';
            img.onload = () => resolve(img);
            img.onerror = () => reject(new Error('Failed to load image'));
            img.src = dataUrl;
        });
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    // Utility methods for different API providers
    static createQRServerProvider() {
        return new ExternalAPIProvider({
            baseUrl: 'https://api.qrserver.com/v1/create-qr-code/',
            timeout: 8000,
            retries: 3
        });
    }
    static createQuickChartProvider() {
        return new ExternalAPIProvider({
            baseUrl: 'https://quickchart.io/qr',
            timeout: 10000,
            retries: 2
        });
    }
}
exports.ExternalAPIProvider = ExternalAPIProvider;
//# sourceMappingURL=external-api-provider.js.map