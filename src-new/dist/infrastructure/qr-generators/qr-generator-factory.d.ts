/**
 * QR Generator Factory
 * Creates appropriate QR generator based on environment and requirements
 */
import { QRGenerationOptions } from '../../shared/types/pix-types';
export type QRGeneratorType = 'qr-code-styling' | 'external-api' | 'auto';
export interface QRGeneratorResult {
    element: HTMLElement;
    dataUrl: string;
    provider: string;
}
export interface QRGeneratorConfig {
    preferredProvider?: QRGeneratorType;
    fallbackEnabled?: boolean;
    externalApiConfig?: {
        baseUrl?: string;
        timeout?: number;
        retries?: number;
    };
}
export declare class QRGeneratorFactory {
    private qrCodeStylingProvider;
    private externalAPIProvider;
    private config;
    constructor(config?: QRGeneratorConfig);
    generateQRCode(options: QRGenerationOptions): Promise<QRGeneratorResult>;
    generateZeroMarginQRCode(options: QRGenerationOptions): Promise<QRGeneratorResult>;
    generateStandardQRCode(data: string): Promise<QRGeneratorResult>;
    generateWithCustomImage(options: QRGenerationOptions, imageFile: File): Promise<QRGeneratorResult>;
    private selectProvider;
    private generateWithQRCodeStyling;
    private generateWithExternalAPI;
    private normalizeQRCodeStylingResult;
    private normalizeExternalAPIResult;
    static createDefault(): QRGeneratorFactory;
    static createWithPreferredProvider(provider: QRGeneratorType): QRGeneratorFactory;
    static createWithoutFallback(provider: QRGeneratorType): QRGeneratorFactory;
}
//# sourceMappingURL=qr-generator-factory.d.ts.map