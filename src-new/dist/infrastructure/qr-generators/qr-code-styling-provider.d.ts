/**
 * QR Code Styling Provider
 * Implementation using the qr-code-styling library
 */
import { QRGenerationOptions } from '../../shared/types/pix-types';
declare global {
    interface Window {
        QRCodeStyling: any;
    }
}
export interface QRCodeStylingResult {
    element: HTMLElement;
    dataUrl?: string;
}
export declare class QRCodeStylingProvider {
    private static isLibraryAvailable;
    generateQRCode(options: QRGenerationOptions): Promise<QRCodeStylingResult>;
    generateZeroMarginQRCode(options: QRGenerationOptions): Promise<QRCodeStylingResult>;
    generateStandardQRCode(data: string): Promise<QRCodeStylingResult>;
    generateWithCustomImage(options: QRGenerationOptions, imageFile: File): Promise<QRCodeStylingResult>;
    private createTempContainer;
    private waitForRendering;
    private cleanupContainer;
    private extractDataUrl;
    private svgToDataUrl;
    private applyZeroMarginProcessing;
    private processZeroMarginSVG;
    private processZeroMarginCanvas;
}
//# sourceMappingURL=qr-code-styling-provider.d.ts.map