"use strict";
/**
 * QR Generator Factory
 * Creates appropriate QR generator based on environment and requirements
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.QRGeneratorFactory = void 0;
const qr_code_styling_provider_1 = require("./qr-code-styling-provider");
const external_api_provider_1 = require("./external-api-provider");
const pix_errors_1 = require("../../shared/errors/pix-errors");
class QRGeneratorFactory {
    constructor(config = {}) {
        this.config = {
            preferredProvider: 'auto',
            fallbackEnabled: true,
            externalApiConfig: {},
            ...config
        };
        this.qrCodeStylingProvider = new qr_code_styling_provider_1.QRCodeStylingProvider();
        this.externalAPIProvider = new external_api_provider_1.ExternalAPIProvider(this.config.externalApiConfig);
    }
    async generateQRCode(options) {
        const provider = this.selectProvider();
        try {
            switch (provider) {
                case 'qr-code-styling':
                    return await this.generateWithQRCodeStyling(options);
                case 'external-api':
                    return await this.generateWithExternalAPI(options);
                default:
                    throw new pix_errors_1.QRGenerationError(`Unknown provider: ${provider}`);
            }
        }
        catch (error) {
            if (this.config.fallbackEnabled && provider !== 'external-api') {
                console.warn(`Primary provider (${provider}) failed, falling back to external API`);
                return await this.generateWithExternalAPI(options);
            }
            throw error;
        }
    }
    async generateZeroMarginQRCode(options) {
        // Zero margin works best with qr-code-styling library
        try {
            return await this.generateWithQRCodeStyling(options, true);
        }
        catch (error) {
            if (this.config.fallbackEnabled) {
                console.warn('Zero margin generation failed, falling back to external API');
                // External API doesn't have advanced margin control, but we can try
                const modifiedOptions = { ...options, margin: 0 };
                return await this.generateWithExternalAPI(modifiedOptions);
            }
            throw error;
        }
    }
    async generateStandardQRCode(data) {
        // Standard QR codes work well with both providers
        const provider = this.selectProvider();
        try {
            if (provider === 'qr-code-styling') {
                const result = await this.qrCodeStylingProvider.generateStandardQRCode(data);
                return this.normalizeQRCodeStylingResult(result, 'qr-code-styling');
            }
            else {
                const result = await this.externalAPIProvider.generateSimpleQRCode(data);
                return this.normalizeExternalAPIResult(result, 'external-api');
            }
        }
        catch (error) {
            if (this.config.fallbackEnabled && provider !== 'external-api') {
                console.warn('Standard QR generation failed, falling back to external API');
                const result = await this.externalAPIProvider.generateSimpleQRCode(data);
                return this.normalizeExternalAPIResult(result, 'external-api-fallback');
            }
            throw error;
        }
    }
    async generateWithCustomImage(options, imageFile) {
        // Custom images require qr-code-styling library
        try {
            const result = await this.qrCodeStylingProvider.generateWithCustomImage(options, imageFile);
            return this.normalizeQRCodeStylingResult(result, 'qr-code-styling');
        }
        catch (error) {
            if (this.config.fallbackEnabled) {
                console.warn('Custom image QR generation failed, falling back to standard external API');
                // External API can't handle custom images, so we generate without image
                const optionsWithoutImage = { ...options };
                delete optionsWithoutImage.imageOptions;
                const result = await this.externalAPIProvider.generateQRCode(optionsWithoutImage);
                return this.normalizeExternalAPIResult(result, 'external-api-no-image');
            }
            throw error;
        }
    }
    selectProvider() {
        if (this.config.preferredProvider !== 'auto') {
            return this.config.preferredProvider;
        }
        // Auto-selection logic
        if (typeof window !== 'undefined' && window.QRCodeStyling) {
            return 'qr-code-styling';
        }
        return 'external-api';
    }
    async generateWithQRCodeStyling(options, zeroMargin = false) {
        const result = zeroMargin
            ? await this.qrCodeStylingProvider.generateZeroMarginQRCode(options)
            : await this.qrCodeStylingProvider.generateQRCode(options);
        return this.normalizeQRCodeStylingResult(result, 'qr-code-styling');
    }
    async generateWithExternalAPI(options) {
        const result = await this.externalAPIProvider.generateQRCode(options);
        return this.normalizeExternalAPIResult(result, 'external-api');
    }
    normalizeQRCodeStylingResult(result, provider) {
        return {
            element: result.element,
            dataUrl: result.dataUrl || '',
            provider
        };
    }
    normalizeExternalAPIResult(result, provider) {
        return {
            element: result.element,
            dataUrl: result.dataUrl,
            provider
        };
    }
    // Static factory methods
    static createDefault() {
        return new QRGeneratorFactory();
    }
    static createWithPreferredProvider(provider) {
        return new QRGeneratorFactory({ preferredProvider: provider });
    }
    static createWithoutFallback(provider) {
        return new QRGeneratorFactory({
            preferredProvider: provider,
            fallbackEnabled: false
        });
    }
}
exports.QRGeneratorFactory = QRGeneratorFactory;
//# sourceMappingURL=qr-generator-factory.js.map