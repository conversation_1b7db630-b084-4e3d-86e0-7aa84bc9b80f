{"version": 3, "file": "external-api-provider.js", "sourceRoot": "", "sources": ["../../../infrastructure/qr-generators/external-api-provider.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,+DAAmE;AAanE,MAAa,mBAAmB;IAG9B,YAAY,MAAmC;QAC7C,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO,EAAE,6CAA6C;YACtD,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC;YACV,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAA4B;QAC/C,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEtC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBAEvE,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;oBACjB,MAAM,IAAI,KAAK,CAAC,uBAAuB,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;gBACnF,CAAC;gBAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACnC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;gBAC/C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBAEvD,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;YAE9B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBACpC,MAAM,IAAI,8BAAiB,CACzB,wCAAwC,IAAI,CAAC,MAAM,CAAC,OAAO,cACzD,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAC3C,EAAE,CACH,CAAC;gBACJ,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAED,MAAM,IAAI,8BAAiB,CAAC,8BAA8B,CAAC,CAAC;IAC9D,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,IAAY,EACZ,OAA0C,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;QAErE,MAAM,aAAa,GAAwB;YACzC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,QAAQ;YACd,IAAI;YACJ,MAAM,EAAE,CAAC;YACT,SAAS,EAAE;gBACT,UAAU,EAAE,CAAC;gBACb,oBAAoB,EAAE,GAAG;aAC1B;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,KAAK;aACjB;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,CAAC;aACT;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,SAAS;aACjB;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,SAAS;aACjB;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;IAC5C,CAAC;IAEO,WAAW,CAAC,OAA4B;QAC9C,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC;YACjC,IAAI,EAAE,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;YAC1C,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,GAAG,EAAE,OAAO,CAAC,SAAS,CAAC,oBAAoB;YAC3C,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE;YACjC,KAAK,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;YACjD,OAAO,EAAE,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;SAC1D,CAAC,CAAC;QAEH,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC;IACvD,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,GAAW,EAAE,OAAe;QACzD,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;QAEhE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;gBAChC,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,OAAO,EAAE;oBACP,QAAQ,EAAE,WAAW;oBACrB,YAAY,EAAE,2BAA2B;iBAC1C;aACF,CAAC,CAAC;YAEH,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,SAAS,CAAC,CAAC;YAExB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBAC1D,MAAM,IAAI,KAAK,CAAC,yBAAyB,OAAO,IAAI,CAAC,CAAC;YACxD,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,IAAU;QACpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;YAChC,MAAM,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,MAAgB,CAAC,CAAC;YAC1D,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC,CAAC;YAC/E,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAe;QAC9C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC1C,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;YAC5B,GAAG,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;YAC/B,GAAG,CAAC,GAAG,GAAG,aAAa,CAAC;YAExB,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAChC,GAAG,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;YAC9D,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED,8CAA8C;IAC9C,MAAM,CAAC,sBAAsB;QAC3B,OAAO,IAAI,mBAAmB,CAAC;YAC7B,OAAO,EAAE,6CAA6C;YACtD,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,CAAC;SACX,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,wBAAwB;QAC7B,OAAO,IAAI,mBAAmB,CAAC;YAC7B,OAAO,EAAE,0BAA0B;YACnC,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,CAAC;SACX,CAAC,CAAC;IACL,CAAC;CACF;AAlKD,kDAkKC"}