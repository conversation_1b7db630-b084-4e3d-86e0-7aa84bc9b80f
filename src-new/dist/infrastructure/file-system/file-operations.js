"use strict";
/**
 * File Operations Service
 * Handles file system operations and downloads
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileOperationsService = void 0;
const pix_errors_1 = require("../../shared/errors/pix-errors");
class FileOperationsService {
    /**
     * Download data as file
     */
    async downloadFile(data, options) {
        try {
            const blob = typeof data === 'string'
                ? await this.dataUrlToBlob(data)
                : data;
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = this.normalizeFilename(options.filename, options.format);
            // Trigger download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            // Cleanup
            URL.revokeObjectURL(url);
        }
        catch (error) {
            throw new pix_errors_1.QRGenerationError(`File download failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Read file as data URL
     */
    async readFileAsDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const result = e.target?.result;
                if (typeof result === 'string') {
                    resolve(result);
                }
                else {
                    reject(new Error('Failed to read file as data URL'));
                }
            };
            reader.onerror = () => reject(new Error('File reading error'));
            reader.readAsDataURL(file);
        });
    }
    /**
     * Read file as text
     */
    async readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const result = e.target?.result;
                if (typeof result === 'string') {
                    resolve(result);
                }
                else {
                    reject(new Error('Failed to read file as text'));
                }
            };
            reader.onerror = () => reject(new Error('File reading error'));
            reader.readAsText(file);
        });
    }
    /**
     * Validate image file
     */
    validateImageFile(file) {
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp', 'image/svg+xml'];
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (!allowedTypes.includes(file.type)) {
            return {
                isValid: false,
                error: `Tipo de arquivo não suportado. Tipos permitidos: ${allowedTypes.join(', ')}`
            };
        }
        if (file.size > maxSize) {
            return {
                isValid: false,
                error: `Arquivo muito grande. Tamanho máximo: ${maxSize / (1024 * 1024)}MB`
            };
        }
        return { isValid: true };
    }
    /**
     * Get file information
     */
    getFileInfo(file) {
        return {
            name: file.name,
            size: file.size,
            type: file.type,
            lastModified: file.lastModified
        };
    }
    /**
     * Convert canvas to blob
     */
    async canvasToBlob(canvas, format = 'image/png', quality = 0.9) {
        return new Promise((resolve, reject) => {
            canvas.toBlob((blob) => {
                if (blob) {
                    resolve(blob);
                }
                else {
                    reject(new Error('Failed to convert canvas to blob'));
                }
            }, format, quality);
        });
    }
    /**
     * Convert SVG to PNG using canvas
     */
    async svgToPng(svgElement, width, height) {
        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            if (!ctx) {
                reject(new Error('Cannot create canvas context'));
                return;
            }
            canvas.width = width;
            canvas.height = height;
            const svgString = new XMLSerializer().serializeToString(svgElement);
            const img = new Image();
            img.onload = () => {
                ctx.drawImage(img, 0, 0, width, height);
                resolve(canvas.toDataURL('image/png'));
            };
            img.onerror = () => reject(new Error('Failed to load SVG image'));
            const svgBlob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });
            const url = URL.createObjectURL(svgBlob);
            img.src = url;
        });
    }
    /**
     * Create temporary URL for blob
     */
    createObjectURL(data) {
        return URL.createObjectURL(data);
    }
    /**
     * Revoke temporary URL
     */
    revokeObjectURL(url) {
        URL.revokeObjectURL(url);
    }
    async dataUrlToBlob(dataUrl) {
        const response = await fetch(dataUrl);
        return await response.blob();
    }
    normalizeFilename(filename, format) {
        // Remove extension if present
        const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
        // Sanitize filename
        const sanitized = nameWithoutExt.replace(/[^a-z0-9-_]/gi, '_');
        // Add proper extension
        return `${sanitized}.${format}`;
    }
    /**
     * Format file size for display
     */
    static formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0)
            return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
}
exports.FileOperationsService = FileOperationsService;
//# sourceMappingURL=file-operations.js.map