/**
 * File Operations Service
 * Handles file system operations and downloads
 */
export interface DownloadOptions {
    filename: string;
    format: 'png' | 'jpg' | 'svg' | 'webp';
    quality?: number;
}
export interface FileInfo {
    name: string;
    size: number;
    type: string;
    lastModified: number;
}
export declare class FileOperationsService {
    /**
     * Download data as file
     */
    downloadFile(data: string | Blob, options: DownloadOptions): Promise<void>;
    /**
     * Read file as data URL
     */
    readFileAsDataURL(file: File): Promise<string>;
    /**
     * Read file as text
     */
    readFileAsText(file: File): Promise<string>;
    /**
     * Validate image file
     */
    validateImageFile(file: File): {
        isValid: boolean;
        error?: string;
    };
    /**
     * Get file information
     */
    getFileInfo(file: File): FileInfo;
    /**
     * Convert canvas to blob
     */
    canvasToBlob(canvas: HTMLCanvasElement, format?: string, quality?: number): Promise<Blob>;
    /**
     * Convert SVG to PNG using canvas
     */
    svgToPng(svgElement: SVGElement, width: number, height: number): Promise<string>;
    /**
     * Create temporary URL for blob
     */
    createObjectURL(data: Blob | File): string;
    /**
     * Revoke temporary URL
     */
    revokeObjectURL(url: string): void;
    private dataUrlToBlob;
    private normalizeFilename;
    /**
     * Format file size for display
     */
    static formatFileSize(bytes: number): string;
}
//# sourceMappingURL=file-operations.d.ts.map