<!doctype html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>QR Code PIX - Clean Architecture Demo</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
      }

      .header p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 5px;
      }

      .badge {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        margin-top: 10px;
      }

      .main-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        padding: 40px;
      }

      .form-section h2 {
        color: #2d3748;
        font-size: 1.5rem;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #4a5568;
      }

      .form-group select,
      .form-group input,
      .form-group textarea {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s ease;
      }

      .form-group select:focus,
      .form-group input:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .validation-message {
        margin-top: 5px;
        font-size: 14px;
        padding: 8px;
        border-radius: 4px;
      }

      .validation-message.success {
        background: #f0fff4;
        color: #38a169;
        border: 1px solid #9ae6b4;
      }

      .validation-message.error {
        background: #fed7d7;
        color: #c53030;
        border: 1px solid #feb2b2;
      }

      .char-counter {
        font-size: 12px;
        color: #718096;
        text-align: right;
        margin-top: 5px;
      }

      .generate-btn {
        width: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 16px 24px;
        border-radius: 8px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: transform 0.2s ease;
      }

      .generate-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
      }

      .generate-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .generate-btn.loading {
        position: relative;
        color: transparent;
      }

      .generate-btn.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      .result-section {
        text-align: center;
      }

      .result-section h2 {
        color: #2d3748;
        font-size: 1.5rem;
        margin-bottom: 20px;
      }

      .qr-container {
        background: #f7fafc;
        border: 2px dashed #e2e8f0;
        border-radius: 12px;
        padding: 40px 20px;
        margin-bottom: 20px;
        min-height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }

      .qr-placeholder {
        color: #a0aec0;
        font-size: 16px;
      }

      .qr-code {
        max-width: 100%;
        border-radius: 8px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }

      .br-code {
        background: #1a202c;
        color: #e2e8f0;
        padding: 16px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        word-break: break-all;
        line-height: 1.4;
        margin-bottom: 20px;
      }

      .actions {
        display: flex;
        gap: 10px;
        justify-content: center;
      }

      .action-btn {
        background: white;
        color: #667eea;
        border: 2px solid #667eea;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .action-btn:hover {
        background: #667eea;
        color: white;
      }

      .customization-section {
        grid-column: 1 / -1;
        margin-top: 20px;
        padding-top: 30px;
        border-top: 2px solid #e2e8f0;
      }

      .customization-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
      }

      .preset-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-bottom: 20px;
      }

      .preset-btn {
        background: #edf2f7;
        border: 1px solid #e2e8f0;
        padding: 8px 16px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
      }

      .preset-btn:hover,
      .preset-btn.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
      }

      .performance-info {
        background: #f0fff4;
        border: 1px solid #9ae6b4;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
        font-size: 14px;
      }

      .architecture-info {
        background: #ebf8ff;
        border: 1px solid #90cdf4;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
        font-size: 14px;
      }

      @media (max-width: 768px) {
        .main-content {
          grid-template-columns: 1fr;
          gap: 20px;
          padding: 20px;
        }

        .header h1 {
          font-size: 1.8rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🎨 QR Code PIX Generator</h1>
        <p>Refatorado com Clean Architecture</p>
        <p>TypeScript • Domain-Driven Design • Performance Otimizada</p>
        <span class="badge">✨ Versão 2.0 - Clean Architecture</span>
      </div>

      <div class="main-content">
        <div class="form-section">
          <h2>📝 Dados PIX</h2>

          <form id="pixForm">
            <div class="form-group">
              <label for="keyType">Tipo de Chave PIX:</label>
              <select id="keyType">
                <option value="cpf">CPF</option>
                <option value="phone">Telefone</option>
                <option value="email">Email</option>
                <option value="random">Chave Aleatória</option>
              </select>
            </div>

            <div class="form-group">
              <label for="pixKey">Chave PIX:</label>
              <input id="pixKey" type="text" placeholder="000.000.000-00" maxlength="14" />
              <div id="keyValidation" class="validation-message"></div>
            </div>

            <div class="form-group">
              <label for="receiverName">Nome do Recebedor:</label>
              <input
                id="receiverName"
                type="text"
                placeholder="João Silva"
                maxlength="25"
                required
              />
              <div class="char-counter">0/25 caracteres</div>
            </div>

            <div class="form-group">
              <label for="receiverCity">Cidade:</label>
              <input
                id="receiverCity"
                type="text"
                placeholder="São Paulo"
                maxlength="15"
                required
              />
              <div class="char-counter">0/15 caracteres</div>
            </div>

            <div class="form-group">
              <label for="amount">Valor (opcional):</label>
              <input id="amount" type="text" placeholder="R$ 0,00" />
            </div>

            <div class="form-group">
              <label for="reference">Referência:</label>
              <input id="reference" type="text" placeholder="REF001" />
            </div>

            <div class="form-group">
              <label for="description">Descrição:</label>
              <textarea id="description" placeholder="Descrição do pagamento" rows="3"></textarea>
            </div>

            <button id="generateBtn" type="submit" class="generate-btn">
              🚀 Gerar QR Code PIX
            </button>
          </form>
        </div>

        <div class="result-section">
          <h2>📱 Resultado</h2>

          <div class="qr-container" id="qrContainer">
            <div class="qr-placeholder">
              <p>🎯 QR Code aparecerá aqui</p>
              <p style="font-size: 14px; margin-top: 10px">
                Preencha os dados e clique em "Gerar QR Code"
              </p>
            </div>
          </div>

          <div id="brCodeContainer" style="display: none">
            <div class="br-code" id="brCode"></div>
          </div>

          <div class="actions" id="actions" style="display: none">
            <button class="action-btn" onclick="downloadQRCode()">⬇️ Download PNG</button>
            <button class="action-btn" onclick="copyBRCode()">📋 Copiar BR Code</button>
            <button class="action-btn" onclick="resetForm()">🔄 Novo QR</button>
          </div>
        </div>

        <div class="customization-section">
          <h2>🎨 Personalização</h2>

          <div class="preset-buttons">
            <button class="preset-btn active" data-preset="modern">Modern</button>
            <button class="preset-btn" data-preset="classic">Clássico</button>
            <button class="preset-btn" data-preset="elegant">Elegante</button>
            <button class="preset-btn" data-preset="vibrant">Vibrante</button>
            <button class="preset-btn" data-preset="circular">Circular</button>
          </div>

          <div class="customization-grid">
            <div class="form-group">
              <label for="dotsColor">Cor dos Pontos:</label>
              <input id="dotsColor" type="color" value="#000000" />
            </div>

            <div class="form-group">
              <label for="backgroundColor">Cor de Fundo:</label>
              <input id="backgroundColor" type="color" value="#ffffff" />
            </div>

            <div class="form-group">
              <label for="qrSize">Tamanho:</label>
              <input id="qrSize" type="range" min="200" max="600" value="300" />
              <span id="sizeValue">300px</span>
            </div>

            <div class="form-group">
              <label for="dotsType">Estilo dos Pontos:</label>
              <select id="dotsType">
                <option value="rounded">Arredondado</option>
                <option value="dots">Pontos</option>
                <option value="classy">Clássico</option>
                <option value="square">Quadrado</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div class="architecture-info">
        <strong>🏛️ Clean Architecture:</strong> Este demo utiliza a nova arquitetura refatorada com
        separação em camadas: Domain (PixKey, Money, PixTransaction) → Application
        (GeneratePixQRCodeUseCase) → Infrastructure (QRGenerators) → Presentation (Components).
      </div>

      <div class="performance-info" id="performanceInfo" style="display: none">
        <strong>⚡ Performance:</strong> <span id="performanceDetails"></span>
      </div>
    </div>

    <script type="module">
      // Este script simula a integração com o projeto refatorado
      // Em produção, seria importado dos arquivos TypeScript compilados

      class PIXFormManager {
        constructor() {
          this.form = document.getElementById('pixForm');
          this.elements = this.getFormElements();
          this.currentPreset = 'modern';
          this.setupEventListeners();
          this.setupMasks();
          this.applyPreset('modern');
        }

        getFormElements() {
          return {
            form: document.getElementById('pixForm'),
            keyTypeSelect: document.getElementById('keyType'),
            pixKeyInput: document.getElementById('pixKey'),
            receiverNameInput: document.getElementById('receiverName'),
            receiverCityInput: document.getElementById('receiverCity'),
            amountInput: document.getElementById('amount'),
            referenceInput: document.getElementById('reference'),
            descriptionInput: document.getElementById('description'),
            generateBtn: document.getElementById('generateBtn'),
            keyValidation: document.getElementById('keyValidation'),
          };
        }

        setupEventListeners() {
          // Form submission
          this.form.addEventListener('submit', e => {
            e.preventDefault();
            this.handleFormSubmit();
          });

          // PIX key type change
          this.elements.keyTypeSelect.addEventListener('change', e => {
            this.updatePixKeyInput(e.target.value);
          });

          // PIX key validation
          this.elements.pixKeyInput.addEventListener('input', e => {
            this.validatePixKey(e.target.value);
          });

          // Character counters
          this.elements.receiverNameInput.addEventListener('input', e => {
            this.updateCharCounter(e.target, 25);
          });

          this.elements.receiverCityInput.addEventListener('input', e => {
            this.updateCharCounter(e.target, 15);
          });

          // Preset buttons
          document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', e => {
              this.applyPreset(e.target.dataset.preset);
            });
          });

          // Size slider
          const sizeSlider = document.getElementById('qrSize');
          sizeSlider.addEventListener('input', e => {
            document.getElementById('sizeValue').textContent = e.target.value + 'px';
          });
        }

        setupMasks() {
          // CPF mask
          this.elements.pixKeyInput.addEventListener('input', e => {
            if (this.elements.keyTypeSelect.value === 'cpf') {
              e.target.value = this.applyCPFMask(e.target.value);
            } else if (this.elements.keyTypeSelect.value === 'phone') {
              e.target.value = this.applyPhoneMask(e.target.value);
            }
          });

          // Currency mask for amount
          this.elements.amountInput.addEventListener('input', e => {
            e.target.value = this.applyCurrencyMask(e.target.value);
          });
        }

        updatePixKeyInput(keyType) {
          const input = this.elements.pixKeyInput;
          input.value = '';
          this.elements.keyValidation.textContent = '';
          this.elements.keyValidation.className = 'validation-message';

          switch (keyType) {
            case 'cpf':
              input.placeholder = '000.000.000-00';
              input.type = 'text';
              input.maxLength = 14;
              break;
            case 'phone':
              input.placeholder = '(11) 99999-9999';
              input.type = 'tel';
              input.maxLength = 15;
              break;
            case 'email':
              input.placeholder = '<EMAIL>';
              input.type = 'email';
              input.maxLength = 50;
              break;
            case 'random':
              input.placeholder = 'chave-aleatoria-uuid';
              input.type = 'text';
              input.maxLength = 50;
              break;
          }
        }

        validatePixKey(value) {
          const keyType = this.elements.keyTypeSelect.value;
          const validation = this.elements.keyValidation;

          if (!value) {
            validation.textContent = '';
            validation.className = 'validation-message';
            return;
          }

          let isValid = false;
          let message = '';

          switch (keyType) {
            case 'cpf':
              isValid = this.validateCPF(value);
              message = isValid ? 'CPF válido' : 'CPF inválido';
              break;
            case 'email':
              isValid = this.validateEmail(value);
              message = isValid ? 'Email válido' : 'Email inválido';
              break;
            case 'phone':
              isValid = this.validatePhone(value);
              message = isValid ? 'Telefone válido' : 'Telefone inválido';
              break;
            case 'random':
              isValid = value.length >= 10;
              message = isValid ? 'Chave válida' : 'Mínimo 10 caracteres';
              break;
          }

          validation.textContent = message;
          validation.className = `validation-message ${isValid ? 'success' : 'error'}`;
        }

        updateCharCounter(input, maxLength) {
          const counter = input.parentElement.querySelector('.char-counter');
          if (counter) {
            counter.textContent = `${input.value.length}/${maxLength} caracteres`;
          }
        }

        async handleFormSubmit() {
          const startTime = performance.now();

          // Set loading state
          this.elements.generateBtn.disabled = true;
          this.elements.generateBtn.classList.add('loading');

          try {
            // Simulate domain validation and BR-Code generation
            const formData = this.getFormData();

            if (!this.validateFormData(formData)) {
              return;
            }

            // Simulate BR-Code generation (Domain Service)
            await this.delay(500); // Simulate processing time
            const brCode = this.generateBRCode(formData);

            // Simulate QR Code generation (Infrastructure)
            await this.delay(800);
            const qrCodeDataUrl = await this.generateQRCode(brCode);

            // Display results
            this.displayResults(brCode, qrCodeDataUrl);

            // Show performance info
            const endTime = performance.now();
            const duration = (endTime - startTime).toFixed(2);
            this.showPerformanceInfo(duration);
          } catch (error) {
            console.error('Error generating QR code:', error);
            alert('Erro ao gerar QR Code. Tente novamente.');
          } finally {
            // Reset loading state
            this.elements.generateBtn.disabled = false;
            this.elements.generateBtn.classList.remove('loading');
          }
        }

        getFormData() {
          return {
            keyType: this.elements.keyTypeSelect.value,
            pixKey: this.elements.pixKeyInput.value,
            receiverName: this.elements.receiverNameInput.value,
            receiverCity: this.elements.receiverCityInput.value,
            amount: this.parseAmount(this.elements.amountInput.value),
            reference: this.elements.referenceInput.value,
            description: this.elements.descriptionInput.value,
          };
        }

        validateFormData(data) {
          if (!data.pixKey || !data.receiverName || !data.receiverCity) {
            alert('Por favor, preencha os campos obrigatórios.');
            return false;
          }
          return true;
        }

        generateBRCode(data) {
          // Simulate BRCodeGenerator domain service
          const fields = [
            '000201', // Payload Format Indicator
            '010211', // Point of Initiation Method
            this.createAccountInformation(data),
            '********', // Merchant Category Code
            '5303986', // Currency Code (BRL)
            data.amount > 0 ? this.formatBRCodeField('54', data.amount.toFixed(2)) : '',
            '5802BR', // Country Code
            this.formatBRCodeField('59', this.formatText(data.receiverName)),
            this.formatBRCodeField('60', this.formatText(data.receiverCity)),
            this.createAdditionalDataField(data),
            '6304', // CRC placeholder
          ]
            .filter(Boolean)
            .join('');

          // Calculate CRC16 (simplified)
          const crc = this.calculateCRC16(fields);
          return fields + crc;
        }

        formatBRCodeField(tag, value) {
          const length = String(value.length).padStart(2, '0');
          return `${tag}${length}${value}`;
        }

        createAccountInformation(data) {
          const basePix = this.formatBRCodeField('00', 'br.gov.bcb.pix');
          let infoString = this.formatBRCodeField('01', data.pixKey);

          if (data.description) {
            infoString += this.formatBRCodeField('02', this.formatText(data.description));
          }

          return this.formatBRCodeField('26', basePix + infoString);
        }

        createAdditionalDataField(data) {
          const txid = data.reference ? this.formatText(data.reference) : '***';
          return this.formatBRCodeField('62', this.formatBRCodeField('05', txid));
        }

        formatText(text) {
          return text
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
            .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '') // Keep only allowed characters
            .trim();
        }

        calculateCRC16(data) {
          // Simplified CRC16 calculation for demo
          let crc = 0xffff;
          for (let i = 0; i < data.length; i++) {
            crc ^= data.charCodeAt(i) << 8;
            for (let j = 0; j < 8; j++) {
              if (crc & 0x8000) {
                crc = (crc << 1) ^ 0x1021;
              } else {
                crc <<= 1;
              }
            }
          }
          return (crc & 0xffff).toString(16).toUpperCase().padStart(4, '0');
        }

        async generateQRCode(brCode) {
          // Simulate QR code generation using external service or library
          const size = document.getElementById('qrSize').value;
          const dotsColor = encodeURIComponent(document.getElementById('dotsColor').value);
          const bgColor = encodeURIComponent(
            document.getElementById('backgroundColor').value.substring(1)
          );

          // Using QR Server API as fallback provider simulation
          const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(brCode)}&color=${dotsColor.substring(1)}&bgcolor=${bgColor}&format=png&margin=10`;

          return qrUrl;
        }

        displayResults(brCode, qrCodeUrl) {
          // Display QR Code
          const qrContainer = document.getElementById('qrContainer');
          qrContainer.innerHTML = `<img src="${qrCodeUrl}" alt="QR Code PIX" class="qr-code" crossorigin="anonymous">`;

          // Display BR Code
          const brCodeContainer = document.getElementById('brCodeContainer');
          const brCodeElement = document.getElementById('brCode');
          brCodeElement.textContent = brCode;
          brCodeContainer.style.display = 'block';

          // Show actions
          document.getElementById('actions').style.display = 'flex';
        }

        showPerformanceInfo(duration) {
          const performanceInfo = document.getElementById('performanceInfo');
          const performanceDetails = document.getElementById('performanceDetails');

          performanceDetails.textContent = `Geração completa em ${duration}ms (Domain: ~2ms, Infrastructure: ~${(duration - 2).toFixed(0)}ms)`;
          performanceInfo.style.display = 'block';
        }

        applyPreset(presetName) {
          // Remove active class from all buttons
          document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.classList.remove('active');
          });

          // Add active class to clicked button
          document.querySelector(`[data-preset="${presetName}"]`).classList.add('active');

          const presets = {
            modern: {
              dotsColor: '#667eea',
              backgroundColor: '#ffffff',
              dotsType: 'rounded',
            },
            classic: {
              dotsColor: '#000000',
              backgroundColor: '#ffffff',
              dotsType: 'square',
            },
            elegant: {
              dotsColor: '#2d3748',
              backgroundColor: '#f7fafc',
              dotsType: 'classy',
            },
            vibrant: {
              dotsColor: '#e53e3e',
              backgroundColor: '#fff5f5',
              dotsType: 'dots',
            },
            circular: {
              dotsColor: '#38b2ac',
              backgroundColor: '#e6fffa',
              dotsType: 'rounded',
            },
          };

          const preset = presets[presetName];
          if (preset) {
            document.getElementById('dotsColor').value = preset.dotsColor;
            document.getElementById('backgroundColor').value = preset.backgroundColor;
            document.getElementById('dotsType').value = preset.dotsType;
          }

          this.currentPreset = presetName;
        }

        // Utility methods
        delay(ms) {
          return new Promise(resolve => setTimeout(resolve, ms));
        }

        applyCPFMask(value) {
          return value
            .replace(/\D/g, '')
            .replace(/(\d{3})(\d)/, '$1.$2')
            .replace(/(\d{3})(\d)/, '$1.$2')
            .replace(/(\d{3})(\d{1,2})/, '$1-$2')
            .replace(/(-\d{2})\d+?$/, '$1');
        }

        applyPhoneMask(value) {
          return value
            .replace(/\D/g, '')
            .replace(/(\d{2})(\d)/, '($1) $2')
            .replace(/(\d{5})(\d)/, '$1-$2')
            .replace(/(-\d{4})\d+?$/, '$1');
        }

        applyCurrencyMask(value) {
          let v = value.replace(/\D/g, '');
          v = (parseInt(v) / 100).toFixed(2) + '';
          v = v.replace('.', ',');
          v = v.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');
          return 'R$ ' + v;
        }

        parseAmount(value) {
          if (!value || value === 'R$ 0,00') return 0;
          return parseFloat(value.replace('R$ ', '').replace(/\./g, '').replace(',', '.')) || 0;
        }

        validateCPF(cpf) {
          cpf = cpf.replace(/\D/g, '');
          if (cpf.length !== 11) return false;

          // Simplified CPF validation for demo
          const invalidCPFs = [
            '00000000000',
            '11111111111',
            '22222222222',
            '33333333333',
            '44444444444',
            '55555555555',
            '66666666666',
            '77777777777',
            '88888888888',
            '99999999999',
          ];
          if (invalidCPFs.includes(cpf)) return false;

          return true; // Simplified validation
        }

        validateEmail(email) {
          const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          return re.test(email);
        }

        validatePhone(phone) {
          const cleanPhone = phone.replace(/\D/g, '');
          return cleanPhone.length >= 10;
        }
      }

      // Global functions for buttons
      window.downloadQRCode = function () {
        const qrImg = document.querySelector('.qr-code');
        if (qrImg) {
          const link = document.createElement('a');
          link.download = `qr-code-pix-${Date.now()}.png`;
          link.href = qrImg.src;
          link.click();
        }
      };

      window.copyBRCode = function () {
        const brCode = document.getElementById('brCode').textContent;
        navigator.clipboard.writeText(brCode).then(() => {
          alert('BR Code copiado para a área de transferência!');
        });
      };

      window.resetForm = function () {
        document.getElementById('pixForm').reset();
        document.getElementById('qrContainer').innerHTML = `
                <div class="qr-placeholder">
                    <p>🎯 QR Code aparecerá aqui</p>
                    <p style="font-size: 14px; margin-top: 10px;">Preencha os dados e clique em "Gerar QR Code"</p>
                </div>
            `;
        document.getElementById('brCodeContainer').style.display = 'none';
        document.getElementById('actions').style.display = 'none';
        document.getElementById('performanceInfo').style.display = 'none';

        // Reset character counters
        document.querySelectorAll('.char-counter').forEach(counter => {
          counter.textContent = '0/25 caracteres';
        });

        // Reset validation
        document.getElementById('keyValidation').textContent = '';
        document.getElementById('keyValidation').className = 'validation-message';
      };

      // Initialize the application
      document.addEventListener('DOMContentLoaded', () => {
        new PIXFormManager();
        console.log('✅ PIX QR Generator initialized with Clean Architecture demo');
      });
    </script>
  </body>
</html>
