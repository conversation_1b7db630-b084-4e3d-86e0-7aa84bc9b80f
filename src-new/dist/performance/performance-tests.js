"use strict";
/**
 * Performance Tests
 * Benchmarks for critical performance metrics
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.runPerformanceTests = runPerformanceTests;
const pix_key_1 = require("../domain/value-objects/pix-key");
const money_1 = require("../domain/value-objects/money");
const pix_transaction_1 = require("../domain/entities/pix-transaction");
const br_code_generator_1 = require("../domain/services/br-code-generator");
class PerformanceTester {
    async runBenchmark(benchmark) {
        const times = [];
        console.log(`🏃 Running benchmark: ${benchmark.name} (${benchmark.iterations} iterations)`);
        // Warm up
        for (let i = 0; i < 10; i++) {
            benchmark.operation();
        }
        // Actual benchmarking
        for (let i = 0; i < benchmark.iterations; i++) {
            const start = performance.now();
            await benchmark.operation();
            const end = performance.now();
            times.push(end - start);
        }
        const totalTime = times.reduce((a, b) => a + b, 0);
        const averageTime = totalTime / benchmark.iterations;
        const minTime = Math.min(...times);
        const maxTime = Math.max(...times);
        const operationsPerSecond = 1000 / averageTime;
        const success = averageTime <= benchmark.expectedMaxTime;
        return {
            name: benchmark.name,
            iterations: benchmark.iterations,
            totalTime,
            averageTime,
            minTime,
            maxTime,
            operationsPerSecond,
            success,
            expectedMaxTime: benchmark.expectedMaxTime
        };
    }
    printResults(results) {
        console.log('\n📊 Performance Test Results\n');
        console.log('┌─' + '─'.repeat(50) + '┬─' + '─'.repeat(12) + '┬─' + '─'.repeat(8) + '┐');
        console.log('│ Benchmark Name' + ' '.repeat(36) + '│ Avg Time (ms)│ Status │');
        console.log('├─' + '─'.repeat(50) + '┼─' + '─'.repeat(12) + '┼─' + '─'.repeat(8) + '┤');
        results.forEach(result => {
            const name = result.name.padEnd(50);
            const avgTime = result.averageTime.toFixed(3).padStart(12);
            const status = (result.success ? '✅ PASS' : '❌ FAIL').padStart(8);
            console.log(`│ ${name}│ ${avgTime}│ ${status}│`);
        });
        console.log('└─' + '─'.repeat(50) + '┴─' + '─'.repeat(12) + '┴─' + '─'.repeat(8) + '┘');
        const totalTests = results.length;
        const passedTests = results.filter(r => r.success).length;
        console.log(`\n📈 Summary: ${passedTests}/${totalTests} tests passed`);
        if (passedTests < totalTests) {
            console.log('\n⚠️  Performance issues detected:');
            results.filter(r => !r.success).forEach(result => {
                console.log(`   • ${result.name}: ${result.averageTime.toFixed(3)}ms (expected < ${result.expectedMaxTime}ms)`);
            });
        }
        else {
            console.log('\n🎉 All performance benchmarks passed!');
        }
    }
}
// Performance test suite
async function runPerformanceTests() {
    const tester = new PerformanceTester();
    // Sample data for tests
    const createSampleTransaction = () => {
        const pixKey = pix_key_1.PixKey.create('<EMAIL>', 'email');
        const amount = money_1.Money.create(150.75);
        return pix_transaction_1.PixTransaction.create({
            pixKey,
            receiverName: 'João Silva',
            receiverCity: 'São Paulo',
            amount,
            reference: 'REF123',
            description: 'Test payment'
        });
    };
    const benchmarks = [
        {
            name: 'PIX Key Creation (CPF)',
            iterations: 1000,
            operation: () => pix_key_1.PixKey.create('11144477735', 'cpf'),
            expectedMaxTime: 1.0 // 1ms
        },
        {
            name: 'PIX Key Creation (Email)',
            iterations: 1000,
            operation: () => pix_key_1.PixKey.create('<EMAIL>', 'email'),
            expectedMaxTime: 0.5 // 0.5ms
        },
        {
            name: 'PIX Key Creation (Phone)',
            iterations: 1000,
            operation: () => pix_key_1.PixKey.create('11999887766', 'phone'),
            expectedMaxTime: 1.0 // 1ms
        },
        {
            name: 'Money Creation',
            iterations: 10000,
            operation: () => money_1.Money.create(Math.random() * 1000),
            expectedMaxTime: 0.1 // 0.1ms
        },
        {
            name: 'Money Addition',
            iterations: 10000,
            operation: () => {
                const money1 = money_1.Money.create(100.50);
                const money2 = money_1.Money.create(50.25);
                return money1.add(money2);
            },
            expectedMaxTime: 0.2 // 0.2ms
        },
        {
            name: 'PIX Transaction Creation',
            iterations: 1000,
            operation: () => {
                const pixKey = pix_key_1.PixKey.create('<EMAIL>', 'email');
                const amount = money_1.Money.create(100.50);
                return pix_transaction_1.PixTransaction.create({
                    pixKey,
                    receiverName: 'João Silva',
                    receiverCity: 'São Paulo',
                    amount
                });
            },
            expectedMaxTime: 2.0 // 2ms
        },
        {
            name: 'BR Code Generation (Basic)',
            iterations: 100,
            operation: () => {
                const transaction = createSampleTransaction();
                return br_code_generator_1.BRCodeGenerator.generate(transaction);
            },
            expectedMaxTime: 5.0 // 5ms
        },
        {
            name: 'BR Code Generation (Complex)',
            iterations: 100,
            operation: () => {
                const pixKey = pix_key_1.PixKey.create('11144477735', 'cpf');
                const amount = money_1.Money.create(999.99);
                const transaction = pix_transaction_1.PixTransaction.create({
                    pixKey,
                    receiverName: 'João Silva dos Santos',
                    receiverCity: 'São Paulo',
                    amount,
                    reference: 'REF-' + Math.random().toString(36).substring(7),
                    description: 'Payment for services with long description text'
                });
                return br_code_generator_1.BRCodeGenerator.generate(transaction, true); // Dynamic
            },
            expectedMaxTime: 8.0 // 8ms
        },
        {
            name: 'CPF Validation (Valid)',
            iterations: 1000,
            operation: () => pix_key_1.PixKey.create('11144477735', 'cpf'),
            expectedMaxTime: 1.0 // 1ms
        },
        {
            name: 'Email Validation',
            iterations: 10000,
            operation: () => pix_key_1.PixKey.create('<EMAIL>', 'email'),
            expectedMaxTime: 0.3 // 0.3ms
        },
        {
            name: 'Brazilian Currency Formatting',
            iterations: 10000,
            operation: () => money_1.Money.create(1234.56).toBrazilianCurrency(),
            expectedMaxTime: 0.1 // 0.1ms
        },
        {
            name: 'Transaction Display Details',
            iterations: 1000,
            operation: () => {
                const transaction = createSampleTransaction();
                return transaction.getDisplayDetails();
            },
            expectedMaxTime: 1.0 // 1ms
        },
        {
            name: 'Large BR Code Generation',
            iterations: 10,
            operation: () => {
                const pixKey = pix_key_1.PixKey.create('random-key-' + 'x'.repeat(40), 'random');
                const amount = money_1.Money.create(9999999.99);
                const transaction = pix_transaction_1.PixTransaction.create({
                    pixKey,
                    receiverName: 'A'.repeat(25), // Max length
                    receiverCity: 'B'.repeat(15), // Max length
                    amount,
                    reference: 'REF-' + 'C'.repeat(20),
                    description: 'D'.repeat(100) // Long description
                });
                return br_code_generator_1.BRCodeGenerator.generate(transaction);
            },
            expectedMaxTime: 15.0 // 15ms
        },
        {
            name: 'Sequential Transaction Processing',
            iterations: 50,
            operation: () => {
                const transactions = [];
                for (let i = 0; i < 10; i++) {
                    const pixKey = pix_key_1.PixKey.create(`user${i}@example.com`, 'email');
                    const amount = money_1.Money.create(100 + i);
                    const transaction = pix_transaction_1.PixTransaction.create({
                        pixKey,
                        receiverName: `User ${i}`,
                        receiverCity: 'City',
                        amount
                    });
                    transactions.push(br_code_generator_1.BRCodeGenerator.generate(transaction));
                }
                return transactions;
            },
            expectedMaxTime: 25.0 // 25ms for 10 transactions
        }
    ];
    const results = [];
    for (const benchmark of benchmarks) {
        try {
            const result = await tester.runBenchmark(benchmark);
            results.push(result);
        }
        catch (error) {
            console.error(`❌ Benchmark failed: ${benchmark.name}`, error);
            results.push({
                name: benchmark.name,
                iterations: benchmark.iterations,
                totalTime: 0,
                averageTime: Infinity,
                minTime: 0,
                maxTime: 0,
                operationsPerSecond: 0,
                success: false,
                expectedMaxTime: benchmark.expectedMaxTime
            });
        }
    }
    tester.printResults(results);
    // Memory usage test
    console.log('\n🧠 Memory Usage Analysis');
    const memoryBefore = process.memoryUsage();
    // Create many objects to test memory efficiency
    const objects = [];
    for (let i = 0; i < 10000; i++) {
        const pixKey = pix_key_1.PixKey.create('<EMAIL>', 'email');
        const amount = money_1.Money.create(100);
        const transaction = pix_transaction_1.PixTransaction.create({
            pixKey,
            receiverName: 'Test User',
            receiverCity: 'Test City',
            amount
        });
        objects.push({ pixKey, amount, transaction });
    }
    const memoryAfter = process.memoryUsage();
    const memoryIncrease = memoryAfter.heapUsed - memoryBefore.heapUsed;
    const memoryPerObject = memoryIncrease / 10000;
    console.log(`Memory per object: ${(memoryPerObject / 1024).toFixed(2)} KB`);
    console.log(`Total memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)} MB`);
    if (memoryPerObject > 1024) { // More than 1KB per object is concerning
        console.log('⚠️  High memory usage detected');
    }
    else {
        console.log('✅ Memory usage is efficient');
    }
    // Exit with error code if any performance tests failed
    const failedTests = results.filter(r => !r.success).length;
    if (failedTests > 0) {
        process.exit(1);
    }
}
// CLI interface
if (require.main === module) {
    runPerformanceTests().catch(error => {
        console.error('Performance test suite failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=performance-tests.js.map