{"version": 3, "file": "performance-tests.js", "sourceRoot": "", "sources": ["../../performance/performance-tests.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAmGH,kDA6NC;AA9TD,6DAAyD;AACzD,yDAAsD;AACtD,wEAAoE;AACpE,4EAAuE;AAqBvE,MAAM,iBAAiB;IAErB,KAAK,CAAC,YAAY,CAAC,SAA+B;QAChD,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,OAAO,CAAC,GAAG,CAAC,yBAAyB,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,UAAU,cAAc,CAAC,CAAC;QAE5F,UAAU;QACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,SAAS,CAAC,SAAS,EAAE,CAAC;QACxB,CAAC;QAED,sBAAsB;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;YAChC,MAAM,SAAS,CAAC,SAAS,EAAE,CAAC;YAC5B,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;YAC9B,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;QAC1B,CAAC;QAED,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,SAAS,GAAG,SAAS,CAAC,UAAU,CAAC;QACrD,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;QACnC,MAAM,mBAAmB,GAAG,IAAI,GAAG,WAAW,CAAC;QAC/C,MAAM,OAAO,GAAG,WAAW,IAAI,SAAS,CAAC,eAAe,CAAC;QAEzD,OAAO;YACL,IAAI,EAAE,SAAS,CAAC,IAAI;YACpB,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,SAAS;YACT,WAAW;YACX,OAAO;YACP,OAAO;YACP,mBAAmB;YACnB,OAAO;YACP,eAAe,EAAE,SAAS,CAAC,eAAe;SAC3C,CAAC;IACJ,CAAC;IAED,YAAY,CAAC,OAA0B;QACrC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QACxF,OAAO,CAAC,GAAG,CAAC,kBAAkB,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,2BAA2B,CAAC,CAAC;QAC/E,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QAExF,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACpC,MAAM,OAAO,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC3D,MAAM,MAAM,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAElE,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,KAAK,OAAO,KAAK,MAAM,GAAG,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QAExF,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;QAClC,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAE1D,OAAO,CAAC,GAAG,CAAC,iBAAiB,WAAW,IAAI,UAAU,eAAe,CAAC,CAAC;QAEvE,IAAI,WAAW,GAAG,UAAU,EAAE,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;YAClD,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC/C,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,kBAAkB,MAAM,CAAC,eAAe,KAAK,CAAC,CAAC;YAClH,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;CACF;AAED,yBAAyB;AAClB,KAAK,UAAU,mBAAmB;IACvC,MAAM,MAAM,GAAG,IAAI,iBAAiB,EAAE,CAAC;IAEvC,wBAAwB;IACxB,MAAM,uBAAuB,GAAG,GAAG,EAAE;QACnC,MAAM,MAAM,GAAG,gBAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;QAC1D,MAAM,MAAM,GAAG,aAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACpC,OAAO,gCAAc,CAAC,MAAM,CAAC;YAC3B,MAAM;YACN,YAAY,EAAE,YAAY;YAC1B,YAAY,EAAE,WAAW;YACzB,MAAM;YACN,SAAS,EAAE,QAAQ;YACnB,WAAW,EAAE,cAAc;SAC5B,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,UAAU,GAA2B;QACzC;YACE,IAAI,EAAE,wBAAwB;YAC9B,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,GAAG,EAAE,CAAC,gBAAM,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC;YACpD,eAAe,EAAE,GAAG,CAAC,MAAM;SAC5B;QACD;YACE,IAAI,EAAE,0BAA0B;YAChC,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,GAAG,EAAE,CAAC,gBAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC;YAC3D,eAAe,EAAE,GAAG,CAAC,QAAQ;SAC9B;QACD;YACE,IAAI,EAAE,0BAA0B;YAChC,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,GAAG,EAAE,CAAC,gBAAM,CAAC,MAAM,CAAC,aAAa,EAAE,OAAO,CAAC;YACtD,eAAe,EAAE,GAAG,CAAC,MAAM;SAC5B;QACD;YACE,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,GAAG,EAAE,CAAC,aAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;YACnD,eAAe,EAAE,GAAG,CAAC,QAAQ;SAC9B;QACD;YACE,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,GAAG,EAAE;gBACd,MAAM,MAAM,GAAG,aAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACpC,MAAM,MAAM,GAAG,aAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACnC,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC5B,CAAC;YACD,eAAe,EAAE,GAAG,CAAC,QAAQ;SAC9B;QACD;YACE,IAAI,EAAE,0BAA0B;YAChC,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,GAAG,EAAE;gBACd,MAAM,MAAM,GAAG,gBAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;gBAC1D,MAAM,MAAM,GAAG,aAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACpC,OAAO,gCAAc,CAAC,MAAM,CAAC;oBAC3B,MAAM;oBACN,YAAY,EAAE,YAAY;oBAC1B,YAAY,EAAE,WAAW;oBACzB,MAAM;iBACP,CAAC,CAAC;YACL,CAAC;YACD,eAAe,EAAE,GAAG,CAAC,MAAM;SAC5B;QACD;YACE,IAAI,EAAE,4BAA4B;YAClC,UAAU,EAAE,GAAG;YACf,SAAS,EAAE,GAAG,EAAE;gBACd,MAAM,WAAW,GAAG,uBAAuB,EAAE,CAAC;gBAC9C,OAAO,mCAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC/C,CAAC;YACD,eAAe,EAAE,GAAG,CAAC,MAAM;SAC5B;QACD;YACE,IAAI,EAAE,8BAA8B;YACpC,UAAU,EAAE,GAAG;YACf,SAAS,EAAE,GAAG,EAAE;gBACd,MAAM,MAAM,GAAG,gBAAM,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;gBACnD,MAAM,MAAM,GAAG,aAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACpC,MAAM,WAAW,GAAG,gCAAc,CAAC,MAAM,CAAC;oBACxC,MAAM;oBACN,YAAY,EAAE,uBAAuB;oBACrC,YAAY,EAAE,WAAW;oBACzB,MAAM;oBACN,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;oBAC3D,WAAW,EAAE,iDAAiD;iBAC/D,CAAC,CAAC;gBACH,OAAO,mCAAe,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,UAAU;YAChE,CAAC;YACD,eAAe,EAAE,GAAG,CAAC,MAAM;SAC5B;QACD;YACE,IAAI,EAAE,wBAAwB;YAC9B,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,GAAG,EAAE,CAAC,gBAAM,CAAC,MAAM,CAAC,aAAa,EAAE,KAAK,CAAC;YACpD,eAAe,EAAE,GAAG,CAAC,MAAM;SAC5B;QACD;YACE,IAAI,EAAE,kBAAkB;YACxB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,GAAG,EAAE,CAAC,gBAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC;YAC3D,eAAe,EAAE,GAAG,CAAC,QAAQ;SAC9B;QACD;YACE,IAAI,EAAE,+BAA+B;YACrC,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,GAAG,EAAE,CAAC,aAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,mBAAmB,EAAE;YAC5D,eAAe,EAAE,GAAG,CAAC,QAAQ;SAC9B;QACD;YACE,IAAI,EAAE,6BAA6B;YACnC,UAAU,EAAE,IAAI;YAChB,SAAS,EAAE,GAAG,EAAE;gBACd,MAAM,WAAW,GAAG,uBAAuB,EAAE,CAAC;gBAC9C,OAAO,WAAW,CAAC,iBAAiB,EAAE,CAAC;YACzC,CAAC;YACD,eAAe,EAAE,GAAG,CAAC,MAAM;SAC5B;QACD;YACE,IAAI,EAAE,0BAA0B;YAChC,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,GAAG,EAAE;gBACd,MAAM,MAAM,GAAG,gBAAM,CAAC,MAAM,CAAC,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;gBACvE,MAAM,MAAM,GAAG,aAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBACxC,MAAM,WAAW,GAAG,gCAAc,CAAC,MAAM,CAAC;oBACxC,MAAM;oBACN,YAAY,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,aAAa;oBAC3C,YAAY,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAG,aAAa;oBAC5C,MAAM;oBACN,SAAS,EAAE,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;oBAClC,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,mBAAmB;iBACjD,CAAC,CAAC;gBACH,OAAO,mCAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC/C,CAAC;YACD,eAAe,EAAE,IAAI,CAAC,OAAO;SAC9B;QACD;YACE,IAAI,EAAE,mCAAmC;YACzC,UAAU,EAAE,EAAE;YACd,SAAS,EAAE,GAAG,EAAE;gBACd,MAAM,YAAY,GAAG,EAAE,CAAC;gBACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC5B,MAAM,MAAM,GAAG,gBAAM,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;oBAC9D,MAAM,MAAM,GAAG,aAAK,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;oBACrC,MAAM,WAAW,GAAG,gCAAc,CAAC,MAAM,CAAC;wBACxC,MAAM;wBACN,YAAY,EAAE,QAAQ,CAAC,EAAE;wBACzB,YAAY,EAAE,MAAM;wBACpB,MAAM;qBACP,CAAC,CAAC;oBACH,YAAY,CAAC,IAAI,CAAC,mCAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;gBAC3D,CAAC;gBACD,OAAO,YAAY,CAAC;YACtB,CAAC;YACD,eAAe,EAAE,IAAI,CAAC,2BAA2B;SAClD;KACF,CAAC;IAEF,MAAM,OAAO,GAAsB,EAAE,CAAC;IAEtC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACnC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,SAAS,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,UAAU,EAAE,SAAS,CAAC,UAAU;gBAChC,SAAS,EAAE,CAAC;gBACZ,WAAW,EAAE,QAAQ;gBACrB,OAAO,EAAE,CAAC;gBACV,OAAO,EAAE,CAAC;gBACV,mBAAmB,EAAE,CAAC;gBACtB,OAAO,EAAE,KAAK;gBACd,eAAe,EAAE,SAAS,CAAC,eAAe;aAC3C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IAE7B,oBAAoB;IACpB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC1C,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAE3C,gDAAgD;IAChD,MAAM,OAAO,GAAG,EAAE,CAAC;IACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,MAAM,GAAG,gBAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;QAC1D,MAAM,MAAM,GAAG,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,WAAW,GAAG,gCAAc,CAAC,MAAM,CAAC;YACxC,MAAM;YACN,YAAY,EAAE,WAAW;YACzB,YAAY,EAAE,WAAW;YACzB,MAAM;SACP,CAAC,CAAC;QACH,OAAO,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAC1C,MAAM,cAAc,GAAG,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;IACpE,MAAM,eAAe,GAAG,cAAc,GAAG,KAAK,CAAC;IAE/C,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAC5E,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IAEtF,IAAI,eAAe,GAAG,IAAI,EAAE,CAAC,CAAC,yCAAyC;QACrE,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAChD,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC7C,CAAC;IAED,uDAAuD;IACvD,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;IAC3D,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;QACpB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,gBAAgB;AAChB,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,mBAAmB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QAClC,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC"}