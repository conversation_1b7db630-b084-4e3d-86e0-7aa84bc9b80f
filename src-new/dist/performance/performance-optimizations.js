"use strict";
/**
 * Performance Optimizations
 * Advanced optimization techniques for critical performance paths
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PerformanceUtils = exports.PixWorkerPool = exports.PerformanceMonitor = exports.LazyPixTransactionLoader = exports.BRCodeBuilder = exports.BatchPixProcessor = exports.OptimizedMoney = exports.OptimizedPixKey = void 0;
exports.runOptimizationBenchmark = runOptimizationBenchmark;
const pix_key_1 = require("../domain/value-objects/pix-key");
const money_1 = require("../domain/value-objects/money");
const pix_transaction_1 = require("../domain/entities/pix-transaction");
// Cache for frequently accessed validation patterns
const VALIDATION_CACHE = new Map();
// Object pool for reusable objects to reduce GC pressure
class ObjectPool {
    constructor(createFn, resetFn, initialSize = 10) {
        this.pool = [];
        this.createFn = createFn;
        this.resetFn = resetFn;
        // Pre-populate pool
        for (let i = 0; i < initialSize; i++) {
            this.pool.push(this.createFn());
        }
    }
    get() {
        if (this.pool.length > 0) {
            return this.pool.pop();
        }
        return this.createFn();
    }
    release(obj) {
        this.resetFn(obj);
        this.pool.push(obj);
    }
    size() {
        return this.pool.length;
    }
}
// Optimized validation caching
class OptimizedPixKey {
    static createCached(value, type) {
        const cacheKey = `${type}:${value}`;
        if (!this.validationCache.has(cacheKey)) {
            try {
                const pixKey = pix_key_1.PixKey.create(value, type);
                this.validationCache.set(cacheKey, true);
                return pixKey;
            }
            catch (error) {
                this.validationCache.set(cacheKey, false);
                throw error;
            }
        }
        const isValid = this.validationCache.get(cacheKey);
        if (!isValid) {
            throw new Error(`Cached validation failed for ${type}: ${value}`);
        }
        return pix_key_1.PixKey.create(value, type); // Still need to create the object
    }
    static clearCache() {
        this.validationCache.clear();
    }
    static getCacheSize() {
        return this.validationCache.size;
    }
}
exports.OptimizedPixKey = OptimizedPixKey;
OptimizedPixKey.validationCache = VALIDATION_CACHE;
// Optimized Money operations using integer math for precision
class OptimizedMoney {
    static createFromCents(cents) {
        return money_1.Money.create(cents / this.PRECISION_MULTIPLIER);
    }
    static addFast(money1, money2) {
        // Convert to cents for integer arithmetic
        const cents1 = Math.round(money1.getValue() * this.PRECISION_MULTIPLIER);
        const cents2 = Math.round(money2.getValue() * this.PRECISION_MULTIPLIER);
        return this.createFromCents(cents1 + cents2);
    }
    static multiplyFast(money, multiplier) {
        const cents = Math.round(money.getValue() * this.PRECISION_MULTIPLIER);
        return this.createFromCents(Math.round(cents * multiplier));
    }
}
exports.OptimizedMoney = OptimizedMoney;
OptimizedMoney.PRECISION_MULTIPLIER = 100;
// Batch processing for multiple transactions
class BatchPixProcessor {
    constructor() {
        this.pendingTransactions = [];
        this.processingPromise = null;
    }
    addTransaction(transaction) {
        this.pendingTransactions.push(transaction);
        return new Promise((resolve) => {
            if (this.pendingTransactions.length >= BatchPixProcessor.BATCH_SIZE) {
                this.flush().then((results) => {
                    const index = results.indexOf(transaction);
                    const result = results[index];
                    if (result) {
                        resolve(result);
                    }
                });
            }
            else {
                // Auto-flush after a short delay if batch isn't full
                setTimeout(() => {
                    this.flush().then((results) => {
                        const index = results.indexOf(transaction);
                        if (index !== -1) {
                            const result = results[index];
                            if (result) {
                                resolve(result);
                            }
                        }
                    });
                }, 10);
            }
        });
    }
    async flush() {
        if (this.processingPromise) {
            return this.processingPromise;
        }
        const batch = [...this.pendingTransactions];
        this.pendingTransactions = [];
        this.processingPromise = this.processBatch(batch);
        const result = await this.processingPromise;
        this.processingPromise = null;
        return result;
    }
    async processBatch(transactions) {
        // Simulate batch processing optimization
        return transactions.map(transaction => {
            // In real implementation, this would optimize validation and processing
            return transaction;
        });
    }
}
exports.BatchPixProcessor = BatchPixProcessor;
BatchPixProcessor.BATCH_SIZE = 100;
// Memory-efficient string builder for BR-Code generation
class BRCodeBuilder {
    constructor() {
        this.chunks = [];
        this.totalLength = 0;
    }
    append(str) {
        this.chunks.push(str);
        this.totalLength += str.length;
        return this;
    }
    build() {
        if (this.chunks.length === 0)
            return '';
        if (this.chunks.length === 1)
            return this.chunks[0] || '';
        // Pre-allocate the exact size needed
        const result = this.chunks.join('');
        this.clear();
        return result;
    }
    clear() {
        this.chunks = [];
        this.totalLength = 0;
    }
    getEstimatedLength() {
        return this.totalLength;
    }
}
exports.BRCodeBuilder = BRCodeBuilder;
// Lazy loading utilities for large datasets
class LazyPixTransactionLoader {
    constructor(loader) {
        this.cache = new Map();
        this.loader = loader;
    }
    async get(id) {
        if (this.cache.has(id)) {
            return this.cache.get(id);
        }
        const transaction = await this.loader(id);
        this.cache.set(id, transaction);
        return transaction;
    }
    preload(ids) {
        const loadPromises = ids
            .filter(id => !this.cache.has(id))
            .map(id => this.get(id).then(() => void 0));
        return Promise.all(loadPromises);
    }
    clearCache() {
        this.cache.clear();
    }
    getCacheSize() {
        return this.cache.size;
    }
}
exports.LazyPixTransactionLoader = LazyPixTransactionLoader;
// Performance monitoring utilities
class PerformanceMonitor {
    static startMeasure(name) {
        const start = performance.now();
        return () => {
            const duration = performance.now() - start;
            if (!this.measurements.has(name)) {
                this.measurements.set(name, []);
            }
            this.measurements.get(name).push(duration);
            return duration;
        };
    }
    static getStats(name) {
        const measurements = this.measurements.get(name);
        if (!measurements || measurements.length === 0) {
            return null;
        }
        const total = measurements.reduce((sum, val) => sum + val, 0);
        return {
            count: measurements.length,
            average: total / measurements.length,
            min: Math.min(...measurements),
            max: Math.max(...measurements),
            total
        };
    }
    static getAllStats() {
        const result = {};
        for (const [name] of this.measurements) {
            result[name] = this.getStats(name);
        }
        return result;
    }
    static clear() {
        this.measurements.clear();
    }
}
exports.PerformanceMonitor = PerformanceMonitor;
PerformanceMonitor.measurements = new Map();
// Worker utilities for CPU-intensive tasks
class PixWorkerPool {
    constructor(workerScript, poolSize = navigator.hardwareConcurrency || 4) {
        this.workers = [];
        this.taskQueue = [];
        this.availableWorkers = [];
        for (let i = 0; i < poolSize; i++) {
            const worker = new Worker(workerScript);
            this.workers.push(worker);
            this.availableWorkers.push(worker);
        }
    }
    async execute(task) {
        return new Promise((resolve, reject) => {
            this.taskQueue.push({ task, resolve, reject });
            this.processQueue();
        });
    }
    processQueue() {
        if (this.taskQueue.length === 0 || this.availableWorkers.length === 0) {
            return;
        }
        const worker = this.availableWorkers.pop();
        const { task, resolve, reject } = this.taskQueue.shift();
        const messageHandler = (event) => {
            worker.removeEventListener('message', messageHandler);
            worker.removeEventListener('error', errorHandler);
            this.availableWorkers.push(worker);
            resolve(event.data);
            this.processQueue();
        };
        const errorHandler = (error) => {
            worker.removeEventListener('message', messageHandler);
            worker.removeEventListener('error', errorHandler);
            this.availableWorkers.push(worker);
            reject(new Error(error.message));
            this.processQueue();
        };
        worker.addEventListener('message', messageHandler);
        worker.addEventListener('error', errorHandler);
        worker.postMessage(task);
    }
    terminate() {
        this.workers.forEach(worker => worker.terminate());
        this.workers = [];
        this.availableWorkers = [];
        this.taskQueue = [];
    }
}
exports.PixWorkerPool = PixWorkerPool;
// Example usage and performance utilities export
exports.PerformanceUtils = {
    OptimizedPixKey,
    OptimizedMoney,
    BatchPixProcessor,
    BRCodeBuilder,
    LazyPixTransactionLoader,
    PerformanceMonitor,
    PixWorkerPool
};
// Performance testing helper
async function runOptimizationBenchmark() {
    console.log('🚀 Running Performance Optimization Benchmarks...\n');
    // Test cached validation
    const endCacheTest = PerformanceMonitor.startMeasure('cached-validation');
    for (let i = 0; i < 1000; i++) {
        OptimizedPixKey.createCached('<EMAIL>', 'email');
    }
    endCacheTest();
    // Test batch processing
    const batchProcessor = new BatchPixProcessor();
    const endBatchTest = PerformanceMonitor.startMeasure('batch-processing');
    const batchPromises = [];
    for (let i = 0; i < 50; i++) {
        const pixKey = pix_key_1.PixKey.create('<EMAIL>', 'email');
        const amount = money_1.Money.create(100 + i);
        const transaction = pix_transaction_1.PixTransaction.create({
            pixKey,
            receiverName: `User ${i}`,
            receiverCity: 'City',
            amount
        });
        batchPromises.push(batchProcessor.addTransaction(transaction));
    }
    await Promise.all(batchPromises);
    endBatchTest();
    // Display results
    const stats = PerformanceMonitor.getAllStats();
    console.log('📊 Performance Results:');
    for (const [name, stat] of Object.entries(stats)) {
        if (stat) {
            console.log(`  ${name}: avg ${stat.average.toFixed(3)}ms, count ${stat.count}`);
        }
    }
    console.log(`\n💾 Cache sizes:`);
    console.log(`  Validation cache: ${OptimizedPixKey.getCacheSize()} entries`);
    // Cleanup
    OptimizedPixKey.clearCache();
    PerformanceMonitor.clear();
    console.log('\n✅ Optimization benchmark completed!');
}
//# sourceMappingURL=performance-optimizations.js.map