/**
 * Performance Optimizations
 * Advanced optimization techniques for critical performance paths
 */
import { PixKey } from '../domain/value-objects/pix-key';
import { Money } from '../domain/value-objects/money';
import { PixTransaction } from '../domain/entities/pix-transaction';
export declare class OptimizedPixKey {
    private static validationCache;
    static createCached(value: string, type: 'cpf' | 'email' | 'phone' | 'random'): PixKey;
    static clearCache(): void;
    static getCacheSize(): number;
}
export declare class OptimizedMoney {
    private static readonly PRECISION_MULTIPLIER;
    static createFromCents(cents: number): Money;
    static addFast(money1: Money, money2: Money): Money;
    static multiplyFast(money: Money, multiplier: number): Money;
}
export declare class BatchPixProcessor {
    private static readonly BATCH_SIZE;
    private pendingTransactions;
    private processingPromise;
    addTransaction(transaction: PixTransaction): Promise<PixTransaction>;
    flush(): Promise<PixTransaction[]>;
    private processBatch;
}
export declare class BRCodeBuilder {
    private chunks;
    private totalLength;
    append(str: string): this;
    build(): string;
    clear(): void;
    getEstimatedLength(): number;
}
export declare class LazyPixTransactionLoader {
    private cache;
    private loader;
    constructor(loader: (id: string) => Promise<PixTransaction>);
    get(id: string): Promise<PixTransaction>;
    preload(ids: string[]): Promise<void[]>;
    clearCache(): void;
    getCacheSize(): number;
}
export declare class PerformanceMonitor {
    private static measurements;
    static startMeasure(name: string): () => number;
    static getStats(name: string): {
        count: number;
        average: number;
        min: number;
        max: number;
        total: number;
    } | null;
    static getAllStats(): Record<string, ReturnType<typeof PerformanceMonitor.getStats>>;
    static clear(): void;
}
export declare class PixWorkerPool {
    private workers;
    private taskQueue;
    private availableWorkers;
    constructor(workerScript: string, poolSize?: number);
    execute<T>(task: any): Promise<T>;
    private processQueue;
    terminate(): void;
}
export declare const PerformanceUtils: {
    OptimizedPixKey: typeof OptimizedPixKey;
    OptimizedMoney: typeof OptimizedMoney;
    BatchPixProcessor: typeof BatchPixProcessor;
    BRCodeBuilder: typeof BRCodeBuilder;
    LazyPixTransactionLoader: typeof LazyPixTransactionLoader;
    PerformanceMonitor: typeof PerformanceMonitor;
    PixWorkerPool: typeof PixWorkerPool;
};
export declare function runOptimizationBenchmark(): Promise<void>;
//# sourceMappingURL=performance-optimizations.d.ts.map