"use strict";
/**
 * Jest Test Setup
 * Global configuration and utilities for all tests
 */
Object.defineProperty(exports, "__esModule", { value: true });
// Mock global objects that may not be available in test environment
global.fetch = jest.fn();
global.FileReader = jest.fn(() => ({
    readAsDataURL: jest.fn(),
    readAsText: jest.fn(),
    result: '',
    onload: null,
    onerror: null,
    onabort: null,
    onloadstart: null,
    onloadend: null,
    onprogress: null,
    readyState: 0,
    error: null,
    EMPTY: 0,
    LOADING: 1,
    DONE: 2
}));
global.Blob = jest.fn();
global.URL = {
    createObjectURL: jest.fn(),
    revokeObjectURL: jest.fn()
};
// Mock DOM APIs
Object.defineProperty(window, 'QRCodeStyling', {
    value: jest.fn().mockImplementation(() => ({
        append: jest.fn(),
        download: jest.fn(),
        toDataURL: jest.fn()
    })),
    writable: true
});
// Mock Canvas API
HTMLCanvasElement.prototype.getContext = jest.fn(() => ({
    fillRect: jest.fn(),
    clearRect: jest.fn(),
    getImageData: jest.fn(() => ({
        data: new Uint8ClampedArray(4)
    })),
    putImageData: jest.fn(),
    createImageData: jest.fn(() => ({
        data: new Uint8ClampedArray(4)
    })),
    setTransform: jest.fn(),
    drawImage: jest.fn(),
    save: jest.fn(),
    fillText: jest.fn(),
    restore: jest.fn(),
    beginPath: jest.fn(),
    moveTo: jest.fn(),
    lineTo: jest.fn(),
    closePath: jest.fn(),
    stroke: jest.fn(),
    translate: jest.fn(),
    scale: jest.fn(),
    rotate: jest.fn(),
    arc: jest.fn(),
    fill: jest.fn(),
    measureText: jest.fn(() => ({ width: 0 })),
    transform: jest.fn(),
    rect: jest.fn(),
    clip: jest.fn()
}));
HTMLCanvasElement.prototype.toDataURL = jest.fn(() => 'data:image/png;base64,mock');
// Mock console methods to reduce noise in tests (except for errors)
const originalError = console.error;
const originalWarn = console.warn;
beforeAll(() => {
    console.log = jest.fn();
    console.info = jest.fn();
    console.warn = jest.fn();
    // Keep console.error for important debugging
    console.error = (...args) => {
        // Only show errors that are not expected test errors
        if (!args[0]?.toString().includes('Warning:') &&
            !args[0]?.toString().includes('ReactDOM.render')) {
            originalError(...args);
        }
    };
});
afterAll(() => {
    console.error = originalError;
    console.warn = originalWarn;
});
// Clean up after each test
afterEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    // Clean up DOM
    document.body.innerHTML = '';
    document.head.innerHTML = '';
});
// Custom matchers
expect.extend({
    toBeValidPixKey(received, keyType) {
        const pass = received && typeof received === 'object' && received.type === keyType;
        if (pass) {
            return {
                message: () => `Expected ${received} not to be a valid PIX key of type ${keyType}`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `Expected ${received} to be a valid PIX key of type ${keyType}`,
                pass: false,
            };
        }
    },
    toHaveValidBRCode(received) {
        // Basic BR Code validation
        const pass = typeof received === 'string' &&
            received.length > 50 &&
            received.includes('0014br.gov.bcb.pix');
        if (pass) {
            return {
                message: () => `Expected ${received} not to be a valid BR Code`,
                pass: true,
            };
        }
        else {
            return {
                message: () => `Expected ${received} to be a valid BR Code`,
                pass: false,
            };
        }
    }
});
//# sourceMappingURL=test-setup.js.map