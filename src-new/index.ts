/**
 * Main entry point for the refactored QR Code Styling library
 * Exports the public API following Clean Architecture principles
 */

// Domain exports (core business logic)
export { Pix<PERSON>ey } from './domain/value-objects/pix-key';
export { Money } from './domain/value-objects/money';
export { PixTransaction } from './domain/entities/pix-transaction';
export { BRCodeGenerator } from './domain/services/br-code-generator';

// Application exports (use cases and DTOs)
export { 
  GeneratePixQRCodeUseCase,
  type GeneratePixQRCodeRequest,
  type GeneratePixQRCodeResponse,
  type QRCodeRepository
} from './application/usecases/generate-pix-qrcode';

// Infrastructure exports (implementations)
import { createQRCodeRepository } from './infrastructure/repositories/qr-code-repository-impl';
import { GeneratePixQRCodeUseCase } from './application/usecases/generate-pix-qrcode';
import { PixController } from './presentation/controllers/pix-controller';
export { 
  QRCodeRepositoryImpl,
  createQRCodeRepository 
} from './infrastructure/repositories/qr-code-repository-impl';

// Presentation exports (controllers)
export { 
  PixController,
  type PixControllerRequest,
  type PixControllerResponse 
} from './presentation/controllers/pix-controller';

// Shared exports (types and utilities)
export * from './shared/types/pix-types';
export * from './shared/errors/pix-errors';

// Factory function for easy setup
export function createPixQRCodeService() {
  const qrCodeRepository = createQRCodeRepository();
  const generatePixQRCodeUseCase = new GeneratePixQRCodeUseCase(qrCodeRepository);
  const pixController = new PixController(generatePixQRCodeUseCase);
  
  return {
    controller: pixController,
    useCase: generatePixQRCodeUseCase,
    repository: qrCodeRepository
  };
}