/**
 * Performance Optimizations
 * Advanced optimization techniques for critical performance paths
 */

import { PixKey } from '../domain/value-objects/pix-key';
import { Money } from '../domain/value-objects/money';
import { PixTransaction } from '../domain/entities/pix-transaction';

// Cache for frequently accessed validation patterns
const VALIDATION_CACHE = new Map<string, boolean>();

// Object pool for reusable objects to reduce GC pressure
class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;

  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    
    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn());
    }
  }

  get(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }

  release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }

  size(): number {
    return this.pool.length;
  }
}

// Optimized validation caching
export class OptimizedPixKey {
  private static validationCache = VALIDATION_CACHE;
  
  static createCached(value: string, type: 'cpf' | 'email' | 'phone' | 'random'): PixKey {
    const cacheKey = `${type}:${value}`;
    
    if (!this.validationCache.has(cacheKey)) {
      try {
        const pixKey = PixKey.create(value, type);
        this.validationCache.set(cacheKey, true);
        return pixKey;
      } catch (error) {
        this.validationCache.set(cacheKey, false);
        throw error;
      }
    }
    
    const isValid = this.validationCache.get(cacheKey);
    if (!isValid) {
      throw new Error(`Cached validation failed for ${type}: ${value}`);
    }
    
    return PixKey.create(value, type); // Still need to create the object
  }

  static clearCache(): void {
    this.validationCache.clear();
  }

  static getCacheSize(): number {
    return this.validationCache.size;
  }
}

// Optimized Money operations using integer math for precision
export class OptimizedMoney {
  private static readonly PRECISION_MULTIPLIER = 100;

  static createFromCents(cents: number): Money {
    return Money.create(cents / this.PRECISION_MULTIPLIER);
  }

  static addFast(money1: Money, money2: Money): Money {
    // Convert to cents for integer arithmetic
    const cents1 = Math.round(money1.getValue() * this.PRECISION_MULTIPLIER);
    const cents2 = Math.round(money2.getValue() * this.PRECISION_MULTIPLIER);
    
    return this.createFromCents(cents1 + cents2);
  }

  static multiplyFast(money: Money, multiplier: number): Money {
    const cents = Math.round(money.getValue() * this.PRECISION_MULTIPLIER);
    return this.createFromCents(Math.round(cents * multiplier));
  }
}

// Batch processing for multiple transactions
export class BatchPixProcessor {
  private static readonly BATCH_SIZE = 100;
  private pendingTransactions: PixTransaction[] = [];
  private processingPromise: Promise<PixTransaction[]> | null = null;

  addTransaction(transaction: PixTransaction): Promise<PixTransaction> {
    this.pendingTransactions.push(transaction);
    
    return new Promise((resolve) => {
      if (this.pendingTransactions.length >= BatchPixProcessor.BATCH_SIZE) {
        this.flush().then((results) => {
          const index = results.indexOf(transaction);
          const result = results[index];
          if (result) {
            resolve(result);
          }
        });
      } else {
        // Auto-flush after a short delay if batch isn't full
        setTimeout(() => {
          this.flush().then((results) => {
            const index = results.indexOf(transaction);
            if (index !== -1) {
              const result = results[index];
              if (result) {
                resolve(result);
              }
            }
          });
        }, 10);
      }
    });
  }

  async flush(): Promise<PixTransaction[]> {
    if (this.processingPromise) {
      return this.processingPromise;
    }

    const batch = [...this.pendingTransactions];
    this.pendingTransactions = [];

    this.processingPromise = this.processBatch(batch);
    const result = await this.processingPromise;
    this.processingPromise = null;

    return result;
  }

  private async processBatch(transactions: PixTransaction[]): Promise<PixTransaction[]> {
    // Simulate batch processing optimization
    return transactions.map(transaction => {
      // In real implementation, this would optimize validation and processing
      return transaction;
    });
  }
}

// Memory-efficient string builder for BR-Code generation
export class BRCodeBuilder {
  private chunks: string[] = [];
  private totalLength = 0;

  append(str: string): this {
    this.chunks.push(str);
    this.totalLength += str.length;
    return this;
  }

  build(): string {
    if (this.chunks.length === 0) return '';
    if (this.chunks.length === 1) return this.chunks[0] || '';

    // Pre-allocate the exact size needed
    const result = this.chunks.join('');
    this.clear();
    return result;
  }

  clear(): void {
    this.chunks = [];
    this.totalLength = 0;
  }

  getEstimatedLength(): number {
    return this.totalLength;
  }
}

// Lazy loading utilities for large datasets
export class LazyPixTransactionLoader {
  private cache = new Map<string, PixTransaction>();
  private loader: (id: string) => Promise<PixTransaction>;

  constructor(loader: (id: string) => Promise<PixTransaction>) {
    this.loader = loader;
  }

  async get(id: string): Promise<PixTransaction> {
    if (this.cache.has(id)) {
      return this.cache.get(id)!;
    }

    const transaction = await this.loader(id);
    this.cache.set(id, transaction);
    return transaction;
  }

  preload(ids: string[]): Promise<void[]> {
    const loadPromises = ids
      .filter(id => !this.cache.has(id))
      .map(id => this.get(id).then(() => void 0));
    
    return Promise.all(loadPromises);
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCacheSize(): number {
    return this.cache.size;
  }
}

// Performance monitoring utilities
export class PerformanceMonitor {
  private static measurements = new Map<string, number[]>();

  static startMeasure(name: string): () => number {
    const start = performance.now();
    
    return (): number => {
      const duration = performance.now() - start;
      
      if (!this.measurements.has(name)) {
        this.measurements.set(name, []);
      }
      
      this.measurements.get(name)!.push(duration);
      return duration;
    };
  }

  static getStats(name: string): {
    count: number;
    average: number;
    min: number;
    max: number;
    total: number;
  } | null {
    const measurements = this.measurements.get(name);
    if (!measurements || measurements.length === 0) {
      return null;
    }

    const total = measurements.reduce((sum, val) => sum + val, 0);
    return {
      count: measurements.length,
      average: total / measurements.length,
      min: Math.min(...measurements),
      max: Math.max(...measurements),
      total
    };
  }

  static getAllStats(): Record<string, ReturnType<typeof PerformanceMonitor.getStats>> {
    const result: Record<string, ReturnType<typeof PerformanceMonitor.getStats>> = {};
    
    for (const [name] of this.measurements) {
      result[name] = this.getStats(name);
    }
    
    return result;
  }

  static clear(): void {
    this.measurements.clear();
  }
}

// Worker utilities for CPU-intensive tasks
export class PixWorkerPool {
  private workers: Worker[] = [];
  private taskQueue: Array<{
    task: any;
    resolve: (result: any) => void;
    reject: (error: Error) => void;
  }> = [];
  private availableWorkers: Worker[] = [];

  constructor(workerScript: string, poolSize: number = navigator.hardwareConcurrency || 4) {
    for (let i = 0; i < poolSize; i++) {
      const worker = new Worker(workerScript);
      this.workers.push(worker);
      this.availableWorkers.push(worker);
    }
  }

  async execute<T>(task: any): Promise<T> {
    return new Promise((resolve, reject) => {
      this.taskQueue.push({ task, resolve, reject });
      this.processQueue();
    });
  }

  private processQueue(): void {
    if (this.taskQueue.length === 0 || this.availableWorkers.length === 0) {
      return;
    }

    const worker = this.availableWorkers.pop()!;
    const { task, resolve, reject } = this.taskQueue.shift()!;

    const messageHandler = (event: MessageEvent) => {
      worker.removeEventListener('message', messageHandler);
      worker.removeEventListener('error', errorHandler);
      
      this.availableWorkers.push(worker);
      resolve(event.data);
      this.processQueue();
    };

    const errorHandler = (error: ErrorEvent) => {
      worker.removeEventListener('message', messageHandler);
      worker.removeEventListener('error', errorHandler);
      
      this.availableWorkers.push(worker);
      reject(new Error(error.message));
      this.processQueue();
    };

    worker.addEventListener('message', messageHandler);
    worker.addEventListener('error', errorHandler);
    worker.postMessage(task);
  }

  terminate(): void {
    this.workers.forEach(worker => worker.terminate());
    this.workers = [];
    this.availableWorkers = [];
    this.taskQueue = [];
  }
}

// Example usage and performance utilities export
export const PerformanceUtils = {
  OptimizedPixKey,
  OptimizedMoney,
  BatchPixProcessor,
  BRCodeBuilder,
  LazyPixTransactionLoader,
  PerformanceMonitor,
  PixWorkerPool
};

// Performance testing helper
export async function runOptimizationBenchmark(): Promise<void> {
  console.log('🚀 Running Performance Optimization Benchmarks...\n');

  // Test cached validation
  const endCacheTest = PerformanceMonitor.startMeasure('cached-validation');
  for (let i = 0; i < 1000; i++) {
    OptimizedPixKey.createCached('<EMAIL>', 'email');
  }
  endCacheTest();

  // Test batch processing
  const batchProcessor = new BatchPixProcessor();
  const endBatchTest = PerformanceMonitor.startMeasure('batch-processing');
  
  const batchPromises = [];
  for (let i = 0; i < 50; i++) {
    const pixKey = PixKey.create('<EMAIL>', 'email');
    const amount = Money.create(100 + i);
    const transaction = PixTransaction.create({
      pixKey,
      receiverName: `User ${i}`,
      receiverCity: 'City',
      amount
    });
    batchPromises.push(batchProcessor.addTransaction(transaction));
  }
  
  await Promise.all(batchPromises);
  endBatchTest();

  // Display results
  const stats = PerformanceMonitor.getAllStats();
  console.log('📊 Performance Results:');
  for (const [name, stat] of Object.entries(stats)) {
    if (stat) {
      console.log(`  ${name}: avg ${stat.average.toFixed(3)}ms, count ${stat.count}`);
    }
  }

  console.log(`\n💾 Cache sizes:`);
  console.log(`  Validation cache: ${OptimizedPixKey.getCacheSize()} entries`);
  
  // Cleanup
  OptimizedPixKey.clearCache();
  PerformanceMonitor.clear();
  
  console.log('\n✅ Optimization benchmark completed!');
}