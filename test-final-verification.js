// Teste final para verificar se a versão refatorada está gerando BR Codes compatíveis

// Simulação da implementação do BRCodeGenerator refatorado
class BRCodeGenerator {
  static PAYLOAD_FORMAT_INDICATOR = '01';
  static POINT_OF_INITIATION_STATIC = '11';
  static PIX_URL = 'br.gov.bcb.pix';
  static MERCHANT_CATEGORY_CODE = '0000';
  static CURRENCY_CODE = '986';
  static COUNTRY_CODE = 'BR';

  static generate(transaction, isDynamic = false) {
    const fields = [
      this.createField('00', BRCodeGenerator.PAYLOAD_FORMAT_INDICATOR),
      this.createField('01', BRCodeGenerator.POINT_OF_INITIATION_STATIC),
      this.createAccountInformation(transaction),
      this.createField('52', BRCodeGenerator.MERCHANT_CATEGORY_CODE),
      this.createField('53', BRCodeGenerator.CURRENCY_CODE)
    ];

    // Add amount only if not zero
    if (!transaction.isFreeAmount()) {
      fields.push(this.createField('54', transaction.amount.toDecimalString()));
    }

    fields.push(
      this.createField('58', BRCodeGenerator.COUNTRY_CODE),
      this.createField('59', this.formatText(transaction.receiverName)),
      this.createField('60', this.formatText(transaction.receiverCity)),
      this.createAdditionalDataField(transaction),
      '6304' // CRC placeholder
    );

    const brCodeWithoutCRC = fields.join('');
    const crc = this.calculateCRC16(brCodeWithoutCRC);
    
    return brCodeWithoutCRC + crc;
  }

  static createAccountInformation(transaction) {
    const basePix = this.createField('00', BRCodeGenerator.PIX_URL);
    let infoString = this.createField('01', transaction.pixKey.value);

    if (transaction.description) {
      infoString += this.createField('02', this.formatText(transaction.description));
    }

    return this.createField('26', basePix + infoString);
  }

  static createAdditionalDataField(transaction) {
    const txid = transaction.reference 
      ? this.formatText(transaction.reference)
      : '***';
    
    return this.createField('62', this.createField('05', txid));
  }

  static createField(tag, value) {
    const length = value.length.toString().padStart(2, '0');
    return `${tag}${length}${value}`;
  }

  static formatText(text) {
    return text
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '') // Keep only allowed characters
      .trim();
  }

  static calculateCRC16(data) {
    let crc = 0xFFFF;
    const dataBytes = new TextEncoder().encode(data);

    for (const byte of dataBytes) {
      crc ^= byte << 8;
      for (let i = 0; i < 8; i++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ 0x1021;
        } else {
          crc <<= 1;
        }
      }
    }

    return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, '0');
  }
}

// Simulação das classes de domínio
class PixKey {
  constructor(value) {
    this.value = value;
  }
}

class Money {
  constructor(amount) {
    this._amount = amount;
  }

  toDecimalString() {
    return this._amount.toFixed(2);
  }

  isZero() {
    return this._amount === 0;
  }
}

class PixTransaction {
  constructor(pixKey, receiverName, receiverCity, amount, reference, description) {
    this.pixKey = pixKey;
    this.receiverName = receiverName;
    this.receiverCity = receiverCity;
    this.amount = amount;
    this.reference = reference;
    this.description = description;
  }

  isFreeAmount() {
    return this.amount.isZero();
  }
}

// Implementação original para comparação
function generateBRCodeOriginal(data) {
  const getValue = (tag, value) => {
    return `${tag}${value.length.toString().padStart(2, "0")}${value}`;
  };

  const formattedText = (text) => {
    return text
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, "");
  };

  const crcCompute = (data) => {
    let crc = 0xffff;
    const dataBytes = new TextEncoder().encode(data);

    for (const byte of dataBytes) {
      crc ^= (byte << 8);
      for (let i = 0; i < 8; i++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ 0x1021;
        } else {
          crc <<= 1;
        }
      }
    }

    return (crc & 0xffff).toString(16).toUpperCase().padStart(4, "0");
  };

  // Build account information
  const basePix = getValue("00", "br.gov.bcb.pix");
  let infoString = getValue("01", data.pixKey);

  if (data.description) {
    infoString += getValue("02", formattedText(data.description));
  }

  const accountInfo = getValue("26", basePix + infoString);

  // Build additional data field
  const txid = data.reference || "***";
  const additionalData = getValue("62", getValue("05", formattedText(txid)));

  // Build complete BR Code
  let resultString = getValue("00", "01"); // Payload Format Indicator
  resultString += getValue("01", "11"); // Point of Initiation Method (static)
  resultString += accountInfo;
  resultString += getValue("52", "0000"); // Merchant Category Code
  resultString += getValue("53", "986"); // Transaction Currency (BRL)

  if (data.amount > 0) {
    resultString += getValue("54", data.amount.toFixed(2));
  }

  resultString += getValue("58", "BR"); // Country Code
  resultString += getValue("59", formattedText(data.receiverName)); // Merchant Name
  resultString += getValue("60", formattedText(data.receiverCity)); // Merchant City
  resultString += additionalData;
  resultString += "6304"; // CRC placeholder

  const finalBRCode = resultString + crcCompute(resultString);

  return finalBRCode;
}

console.log("=== VERIFICAÇÃO FINAL DA VERSÃO REFATORADA ===\n");

const testCases = [
  {
    name: "CPF com valor e descrição",
    data: {
      pixKey: "***********",
      receiverName: "João Silva",
      receiverCity: "São Paulo",
      amount: 150.75,
      reference: "TEST001",
      description: "Pagamento teste"
    }
  },
  {
    name: "Email sem valor",
    data: {
      pixKey: "<EMAIL>",
      receiverName: "Maria Santos",
      receiverCity: "Rio de Janeiro",
      amount: 0,
      reference: "REF123",
      description: ""
    }
  },
  {
    name: "Telefone com caracteres especiais",
    data: {
      pixKey: "+5511999887766",
      receiverName: "José da Silva",
      receiverCity: "São José dos Campos",
      amount: 25.50,
      reference: "",
      description: "Pagamento com acentuação"
    }
  }
];

let allTestsPassed = true;

testCases.forEach((testCase, index) => {
  console.log(`\n--- TESTE ${index + 1}: ${testCase.name} ---`);
  
  // Criar objetos de domínio para a versão refatorada
  const pixKey = new PixKey(testCase.data.pixKey);
  const amount = new Money(testCase.data.amount);
  const transaction = new PixTransaction(
    pixKey,
    testCase.data.receiverName,
    testCase.data.receiverCity,
    amount,
    testCase.data.reference,
    testCase.data.description
  );

  const originalBRCode = generateBRCodeOriginal(testCase.data);
  const refactoredBRCode = BRCodeGenerator.generate(transaction);

  console.log("BR Code Original:");
  console.log(originalBRCode);
  console.log("Tamanho:", originalBRCode.length);
  console.log();

  console.log("BR Code Refatorado:");
  console.log(refactoredBRCode);
  console.log("Tamanho:", refactoredBRCode.length);
  console.log();

  const isEqual = originalBRCode === refactoredBRCode;
  console.log(`Resultado: ${isEqual ? '✅ PASSOU' : '❌ FALHOU'}`);
  
  if (!isEqual) {
    allTestsPassed = false;
    console.log("\n=== DIFERENÇAS ENCONTRADAS ===");
    
    // Encontrar primeira diferença
    for (let i = 0; i < Math.min(originalBRCode.length, refactoredBRCode.length); i++) {
      if (originalBRCode[i] !== refactoredBRCode[i]) {
        console.log(`Primeira diferença na posição ${i}:`);
        console.log(`Original:    "${originalBRCode.substring(i-5, i+10)}"`);
        console.log(`Refatorado:  "${refactoredBRCode.substring(i-5, i+10)}"`);
        console.log(`             ${' '.repeat(5)}^`);
        break;
      }
    }
  }
});

console.log("\n" + "=".repeat(60));
console.log("RESULTADO FINAL:");
if (allTestsPassed) {
  console.log("✅ TODOS OS TESTES PASSARAM!");
  console.log("✅ A versão refatorada está gerando BR Codes compatíveis!");
  console.log("✅ Os QR Codes PIX agora devem ser reconhecidos pelos aplicativos bancários!");
} else {
  console.log("❌ ALGUNS TESTES FALHARAM!");
  console.log("❌ Ainda há problemas na geração dos BR Codes!");
}
console.log("=".repeat(60));
