// Teste para identificar o bug na implementação do demo.html

// Implementação CORRETA (versão original)
function formatAmountCorrect(amount) {
  if (amount <= 0) return '';
  const tag = '54';
  const value = amount.toFixed(2);
  const length = value.length.toString().padStart(2, '0');
  return `${tag}${length}${value}`;
}

// Implementação INCORRETA (demo.html linha 675)
function formatAmountIncorrect(amount) {
  if (amount <= 0) return '';
  const tag = '54';
  const value = amount.toFixed(2);
  const incorrectLength = String(value).padStart(2, '0'); // ERRO: padStart no valor em vez do length
  return `${tag}${incorrectLength}${value}`;
}

// Implementação CORRETA usando formatBRCodeField
function formatAmountWithHelper(amount) {
  if (amount <= 0) return '';
  const formatBRCodeField = (tag, value) => {
    const length = String(value.length).padStart(2, '0');
    return `${tag}${length}${value}`;
  };
  return formatBRCodeField('54', amount.toFixed(2));
}

console.log("=== TESTE DO BUG NA FORMATAÇÃO DO VALOR ===\n");

const testAmounts = [150.75, 25.50, 1000.00, 0.01, 9999.99];

testAmounts.forEach(amount => {
  console.log(`\nTeste com valor: ${amount}`);
  
  const correct = formatAmountCorrect(amount);
  const incorrect = formatAmountIncorrect(amount);
  const withHelper = formatAmountWithHelper(amount);
  
  console.log(`Correto:     "${correct}"`);
  console.log(`Incorreto:   "${incorrect}"`);
  console.log(`Com helper:  "${withHelper}"`);
  console.log(`Correto == Helper: ${correct === withHelper}`);
  console.log(`Correto == Incorreto: ${correct === incorrect}`);
  
  if (correct !== incorrect) {
    console.log("❌ DIFERENÇA ENCONTRADA!");
  } else {
    console.log("✅ Iguais");
  }
});

console.log("\n=== ANÁLISE DO PROBLEMA ===");
console.log("O problema na linha 675 do demo.html é:");
console.log("❌ INCORRETO: `54${String(data.amount.toFixed(2)).padStart(2, '0')}${data.amount.toFixed(2)}`");
console.log("✅ CORRETO:   `54${String(data.amount.toFixed(2).length).padStart(2, '0')}${data.amount.toFixed(2)}`");
console.log("ou melhor ainda:");
console.log("✅ CORRETO:   formatBRCodeField('54', data.amount.toFixed(2))");

// Teste com BR Code completo
console.log("\n=== TESTE COM BR CODE COMPLETO ===");

function generateBRCodeWithBug(data) {
  const formatBRCodeField = (tag, value) => {
    const length = String(value.length).padStart(2, '0');
    return `${tag}${length}${value}`;
  };
  
  const calculateCRC16 = (data) => {
    let crc = 0xFFFF;
    for (let i = 0; i < data.length; i++) {
      crc ^= data.charCodeAt(i) << 8;
      for (let j = 0; j < 8; j++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ 0x1021;
        } else {
          crc <<= 1;
        }
      }
    }
    return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, '0');
  };

  const fields = [
    '000201', // Payload Format Indicator
    '010211', // Point of Initiation Method
    `26${formatBRCodeField('br.gov.bcb.pix', data.pixKey)}`,
    '52040000', // Merchant Category Code
    '5303986', // Currency Code (BRL)
    // BUG REPRODUZIDO:
    data.amount > 0 ? `54${String(data.amount.toFixed(2)).padStart(2, '0')}${data.amount.toFixed(2)}` : '',
    '5802BR', // Country Code
    `59${String(data.receiverName.length).padStart(2, '0')}${data.receiverName}`,
    `60${String(data.receiverCity.length).padStart(2, '0')}${data.receiverCity}`,
    data.reference ? `62${formatBRCodeField('05', data.reference)}` : '620405***',
    '6304' // CRC placeholder
  ].filter(Boolean).join('');

  const crc = calculateCRC16(fields);
  return fields + crc;
}

function generateBRCodeFixed(data) {
  const formatBRCodeField = (tag, value) => {
    const length = String(value.length).padStart(2, '0');
    return `${tag}${length}${value}`;
  };
  
  const calculateCRC16 = (data) => {
    let crc = 0xFFFF;
    for (let i = 0; i < data.length; i++) {
      crc ^= data.charCodeAt(i) << 8;
      for (let j = 0; j < 8; j++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ 0x1021;
        } else {
          crc <<= 1;
        }
      }
    }
    return (crc & 0xFFFF).toString(16).toUpperCase().padStart(4, '0');
  };

  const fields = [
    '000201', // Payload Format Indicator
    '010211', // Point of Initiation Method
    `26${formatBRCodeField('br.gov.bcb.pix', data.pixKey)}`,
    '52040000', // Merchant Category Code
    '5303986', // Currency Code (BRL)
    // CORRIGIDO:
    data.amount > 0 ? formatBRCodeField('54', data.amount.toFixed(2)) : '',
    '5802BR', // Country Code
    `59${String(data.receiverName.length).padStart(2, '0')}${data.receiverName}`,
    `60${String(data.receiverCity.length).padStart(2, '0')}${data.receiverCity}`,
    data.reference ? `62${formatBRCodeField('05', data.reference)}` : '620405***',
    '6304' // CRC placeholder
  ].filter(Boolean).join('');

  const crc = calculateCRC16(fields);
  return fields + crc;
}

const testData = {
  pixKey: "11144477735",
  receiverName: "João Silva",
  receiverCity: "São Paulo",
  amount: 150.75,
  reference: "TEST001"
};

const brCodeWithBug = generateBRCodeWithBug(testData);
const brCodeFixed = generateBRCodeFixed(testData);

console.log(`\nBR Code com bug:  ${brCodeWithBug}`);
console.log(`BR Code corrigido: ${brCodeFixed}`);
console.log(`São iguais: ${brCodeWithBug === brCodeFixed}`);

if (brCodeWithBug !== brCodeFixed) {
  console.log("\n❌ CONFIRMADO: O bug está na formatação do campo de valor!");
  
  // Encontrar a diferença
  for (let i = 0; i < Math.min(brCodeWithBug.length, brCodeFixed.length); i++) {
    if (brCodeWithBug[i] !== brCodeFixed[i]) {
      console.log(`Primeira diferença na posição ${i}:`);
      console.log(`Com bug:  "${brCodeWithBug.substring(i-5, i+10)}"`);
      console.log(`Corrigido: "${brCodeFixed.substring(i-5, i+10)}"`);
      console.log(`           ${' '.repeat(5)}^`);
      break;
    }
  }
}
